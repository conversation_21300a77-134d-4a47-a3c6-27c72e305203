{"version": 3, "file": "browser-data.js", "sourceRoot": "", "sources": ["../../../src/browser-data/browser-data.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmKH,wCAmCC;AAKD,sCAWC;AAKD,kEAgBC;AAQD,oDAIC;AArPD,gFAAkE;AAClE,oDAAsC;AACtC,gEAAkD;AAClD,wDAA0C;AAC1C,sDAAwC;AACxC,yCAMoB;AAoCZ,wFAzCN,kBAAO,OAyCM;AAAE,gGAxCf,0BAAe,OAwCe;AAAE,qGAtChC,+BAAoB,OAsCgC;AAhCzC,QAAA,YAAY,GAAG;IAC1B,CAAC,kBAAO,CAAC,YAAY,CAAC,EAAE,YAAY,CAAC,kBAAkB;IACvD,CAAC,kBAAO,CAAC,mBAAmB,CAAC,EAAE,mBAAmB,CAAC,kBAAkB;IACrE,CAAC,kBAAO,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,kBAAkB;IAC3C,CAAC,kBAAO,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,kBAAkB;IAC/C,CAAC,kBAAO,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,kBAAkB;CAC9C,CAAC;AAEW,QAAA,aAAa,GAAG;IAC3B,CAAC,kBAAO,CAAC,YAAY,CAAC,EAAE,YAAY,CAAC,mBAAmB;IACxD,CAAC,kBAAO,CAAC,mBAAmB,CAAC,EAAE,mBAAmB,CAAC,mBAAmB;IACtE,CAAC,kBAAO,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,mBAAmB;IAC5C,CAAC,kBAAO,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,mBAAmB;IAChD,CAAC,kBAAO,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,mBAAmB;CAC/C,CAAC;AAEW,QAAA,uBAAuB,GAAG;IACrC,CAAC,kBAAO,CAAC,YAAY,CAAC,EAAE,YAAY,CAAC,sBAAsB;IAC3D,CAAC,kBAAO,CAAC,mBAAmB,CAAC,EAAE,mBAAmB,CAAC,sBAAsB;IACzE,CAAC,kBAAO,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,sBAAsB;IAC/C,CAAC,kBAAO,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,sBAAsB;IACnD,CAAC,kBAAO,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,sBAAsB;CAClD,CAAC;AAEW,QAAA,kBAAkB,GAAG;IAChC,CAAC,kBAAO,CAAC,YAAY,CAAC,EAAE,YAAY,CAAC,eAAe;IACpD,CAAC,kBAAO,CAAC,mBAAmB,CAAC,EAAE,mBAAmB,CAAC,eAAe;IAClE,CAAC,kBAAO,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,eAAe;IACxC,CAAC,kBAAO,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,eAAe;IAC5C,CAAC,kBAAO,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,eAAe;CAC3C,CAAC;AAIF;;GAEG;AACH,KAAK,UAAU,2BAA2B,CACxC,OAAgB,EAChB,QAAyB,EACzB,GAAe;IAEf,QAAQ,OAAO,EAAE,CAAC;QAChB,KAAK,kBAAO,CAAC,OAAO;YAClB,QAAQ,GAAG,EAAE,CAAC;gBACZ,KAAK,qBAAU,CAAC,MAAM;oBACpB,OAAO,MAAM,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;gBACtE,KAAK,qBAAU,CAAC,IAAI;oBAClB,OAAO,MAAM,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;gBACnE,KAAK,qBAAU,CAAC,OAAO;oBACrB,OAAO,MAAM,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;gBACtE,KAAK,qBAAU,CAAC,UAAU;oBACxB,OAAO,MAAM,OAAO,CAAC,cAAc,CACjC,OAAO,CAAC,cAAc,CAAC,UAAU,CAClC,CAAC;gBACJ,KAAK,qBAAU,CAAC,MAAM;oBACpB,OAAO,MAAM,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;gBACrE,KAAK,qBAAU,CAAC,GAAG;oBACjB,OAAO,MAAM,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;gBAClE,KAAK,qBAAU,CAAC,MAAM,CAAC;gBACvB,KAAK,qBAAU,CAAC,GAAG;oBACjB,MAAM,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,WAAW,EAAE,+BAA+B,CAAC,CAAC;YACzE,CAAC;QACH,KAAK,kBAAO,CAAC,MAAM,CAAC,CAAC,CAAC;YACpB,QAAQ,GAAG,EAAE,CAAC;gBACZ,KAAK,qBAAU,CAAC,MAAM;oBACpB,OAAO,MAAM,MAAM,CAAC,cAAc,CAAC,+BAAoB,CAAC,MAAM,CAAC,CAAC;gBAClE,KAAK,qBAAU,CAAC,IAAI;oBAClB,OAAO,MAAM,MAAM,CAAC,cAAc,CAAC,+BAAoB,CAAC,IAAI,CAAC,CAAC;gBAChE,KAAK,qBAAU,CAAC,MAAM;oBACpB,OAAO,MAAM,MAAM,CAAC,cAAc,CAAC,+BAAoB,CAAC,MAAM,CAAC,CAAC;gBAClE,KAAK,qBAAU,CAAC,GAAG;oBACjB,OAAO,MAAM,MAAM,CAAC,cAAc,CAAC,+BAAoB,CAAC,GAAG,CAAC,CAAC;gBAC/D,KAAK,qBAAU,CAAC,MAAM;oBACpB,OAAO,MAAM,MAAM,CAAC,cAAc,CAAC,+BAAoB,CAAC,MAAM,CAAC,CAAC;gBAClE,KAAK,qBAAU,CAAC,OAAO,CAAC;gBACxB,KAAK,qBAAU,CAAC,UAAU,CAAC;gBAC3B,KAAK,qBAAU,CAAC,GAAG;oBACjB,MAAM,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,WAAW,EAAE,8BAA8B,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;QACD,KAAK,kBAAO,CAAC,YAAY,CAAC,CAAC,CAAC;YAC1B,QAAQ,GAAG,EAAE,CAAC;gBACZ,KAAK,qBAAU,CAAC,MAAM,CAAC;gBACvB,KAAK,qBAAU,CAAC,MAAM;oBACpB,OAAO,MAAM,YAAY,CAAC,cAAc,CAAC,+BAAoB,CAAC,MAAM,CAAC,CAAC;gBACxE,KAAK,qBAAU,CAAC,IAAI;oBAClB,OAAO,MAAM,YAAY,CAAC,cAAc,CAAC,+BAAoB,CAAC,IAAI,CAAC,CAAC;gBACtE,KAAK,qBAAU,CAAC,GAAG;oBACjB,OAAO,MAAM,YAAY,CAAC,cAAc,CAAC,+BAAoB,CAAC,GAAG,CAAC,CAAC;gBACrE,KAAK,qBAAU,CAAC,MAAM;oBACpB,OAAO,MAAM,YAAY,CAAC,cAAc,CAAC,+BAAoB,CAAC,MAAM,CAAC,CAAC;gBACxE,KAAK,qBAAU,CAAC,OAAO,CAAC;gBACxB,KAAK,qBAAU,CAAC,UAAU,CAAC;gBAC3B,KAAK,qBAAU,CAAC,GAAG;oBACjB,MAAM,IAAI,KAAK,CACb,GAAG,GAAG,CAAC,WAAW,EAAE,oCAAoC,CACzD,CAAC;YACN,CAAC;QACH,CAAC;QACD,KAAK,kBAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC;YACjC,QAAQ,GAAG,EAAE,CAAC;gBACZ,KAAK,qBAAU,CAAC,MAAM,CAAC;gBACvB,KAAK,qBAAU,CAAC,MAAM;oBACpB,OAAO,MAAM,mBAAmB,CAAC,cAAc,CAC7C,+BAAoB,CAAC,MAAM,CAC5B,CAAC;gBACJ,KAAK,qBAAU,CAAC,IAAI;oBAClB,OAAO,MAAM,mBAAmB,CAAC,cAAc,CAC7C,+BAAoB,CAAC,IAAI,CAC1B,CAAC;gBACJ,KAAK,qBAAU,CAAC,GAAG;oBACjB,OAAO,MAAM,mBAAmB,CAAC,cAAc,CAC7C,+BAAoB,CAAC,GAAG,CACzB,CAAC;gBACJ,KAAK,qBAAU,CAAC,MAAM;oBACpB,OAAO,MAAM,mBAAmB,CAAC,cAAc,CAC7C,+BAAoB,CAAC,MAAM,CAC5B,CAAC;gBACJ,KAAK,qBAAU,CAAC,OAAO,CAAC;gBACxB,KAAK,qBAAU,CAAC,UAAU,CAAC;gBAC3B,KAAK,qBAAU,CAAC,GAAG;oBACjB,MAAM,IAAI,KAAK,CAAC,GAAG,GAAG,6CAA6C,CAAC,CAAC;YACzE,CAAC;QACH,CAAC;QACD,KAAK,kBAAO,CAAC,QAAQ;YACnB,QAAQ,GAAG,EAAE,CAAC;gBACZ,KAAK,qBAAU,CAAC,MAAM;oBACpB,OAAO,MAAM,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;gBACjD,KAAK,qBAAU,CAAC,OAAO,CAAC;gBACxB,KAAK,qBAAU,CAAC,MAAM,CAAC;gBACvB,KAAK,qBAAU,CAAC,GAAG,CAAC;gBACpB,KAAK,qBAAU,CAAC,UAAU,CAAC;gBAC3B,KAAK,qBAAU,CAAC,IAAI,CAAC;gBACrB,KAAK,qBAAU,CAAC,MAAM,CAAC;gBACvB,KAAK,qBAAU,CAAC,GAAG;oBACjB,MAAM,IAAI,KAAK,CACb,GAAG,GAAG,uDAAuD,CAC9D,CAAC;YACN,CAAC;IACL,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,cAAc,CAClC,OAAgB,EAChB,QAAyB,EACzB,GAAwB;IAExB,MAAM,UAAU,GAAG,GAAiB,CAAC;IACrC,IAAI,MAAM,CAAC,MAAM,CAAC,qBAAU,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;QACnD,OAAO,MAAM,2BAA2B,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;IAC1E,CAAC;IAED,QAAQ,OAAO,EAAE,CAAC;QAChB,KAAK,kBAAO,CAAC,OAAO;YAClB,OAAO,GAAG,CAAC;QACb,KAAK,kBAAO,CAAC,MAAM;YACjB,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;YACtD,IAAI,YAAY,EAAE,CAAC;gBACjB,OAAO,YAAY,CAAC;YACtB,CAAC;YACD,OAAO,GAAG,CAAC;QACb,KAAK,kBAAO,CAAC,YAAY;YACvB,MAAM,kBAAkB,GAAG,MAAM,YAAY,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;YAClE,IAAI,kBAAkB,EAAE,CAAC;gBACvB,OAAO,kBAAkB,CAAC;YAC5B,CAAC;YACD,OAAO,GAAG,CAAC;QACb,KAAK,kBAAO,CAAC,mBAAmB;YAC9B,MAAM,yBAAyB,GAC7B,MAAM,mBAAmB,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;YAChD,IAAI,yBAAyB,EAAE,CAAC;gBAC9B,OAAO,yBAAyB,CAAC;YACnC,CAAC;YACD,OAAO,GAAG,CAAC;QACb,KAAK,kBAAO,CAAC,QAAQ;YACnB,OAAO,GAAG,CAAC;IACf,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,aAAa,CACjC,OAAgB,EAChB,IAAoB;IAEpB,QAAQ,OAAO,EAAE,CAAC;QAChB,KAAK,kBAAO,CAAC,OAAO;YAClB,OAAO,MAAM,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAC3C,KAAK,kBAAO,CAAC,MAAM,CAAC;QACpB,KAAK,kBAAO,CAAC,QAAQ;YACnB,MAAM,IAAI,KAAK,CAAC,uCAAuC,OAAO,MAAM,CAAC,CAAC;IAC1E,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAgB,2BAA2B,CACzC,OAAgB,EAChB,QAAyB,EACzB,OAA6B;IAE7B,QAAQ,OAAO,EAAE,CAAC;QAChB,KAAK,kBAAO,CAAC,YAAY,CAAC;QAC1B,KAAK,kBAAO,CAAC,mBAAmB,CAAC;QACjC,KAAK,kBAAO,CAAC,OAAO,CAAC;QACrB,KAAK,kBAAO,CAAC,QAAQ;YACnB,MAAM,IAAI,KAAK,CACb,iDAAiD,OAAO,OAAO,CAChE,CAAC;QACJ,KAAK,kBAAO,CAAC,MAAM;YACjB,OAAO,MAAM,CAAC,2BAA2B,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IACjE,CAAC;AACH,CAAC;AAED;;;;;GAKG;AACH,SAAgB,oBAAoB,CAClC,OAAgB;IAEhB,OAAO,0BAAkB,CAAC,OAAO,CAAC,CAAC;AACrC,CAAC"}