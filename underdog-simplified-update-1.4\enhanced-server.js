const express = require('express');
const EnhancedUnderdogService = require('./enhanced-underdog-service');
require('dotenv').config();

class EnhancedServer {
  constructor() {
    this.app = express();
    this.port = process.env.PORT || 3000;
    this.underdogService = null;
    this.isInitializing = false;
    this.initializationError = null;
    
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  setupMiddleware() {
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));
    
    // Request logging
    this.app.use((req, res, next) => {
      console.log(`📥 ${req.method} ${req.path} - ${new Date().toISOString()}`);
      next();
    });
  }

  setupRoutes() {
    // Health check endpoint
    this.app.get('/health', (req, res) => {
      const status = this.getServiceStatus();
      res.status(status.httpCode).json(status.response);
    });

    // Main API endpoint
    this.app.post('/generate-links', async (req, res) => {
      try {
        const { betIds } = req.body;
        
        if (!betIds || !Array.isArray(betIds)) {
          return res.status(400).json({
            share_link: null,
            error: 'Invalid request: betIds array required'
          });
        }

        // Check service status
        const status = this.getServiceStatus();
        if (status.response.status !== 'ready') {
          return res.status(503).json({
            share_link: null,
            error: `Service Unavailable: ${status.response.status}`
          });
        }

        // Process bet IDs (placeholder for actual implementation)
        console.log(`🎯 Processing bet IDs: ${betIds.join(', ')}`);
        
        // For now, return a mock response
        // In actual implementation, this would use the authenticated session
        const shareLink = await this.generateBetLink(betIds);
        
        res.json({
          share_link: shareLink,
          error: null
        });

      } catch (error) {
        console.error('❌ Error processing bet link request:', error);
        res.status(500).json({
          share_link: null,
          error: `Internal server error: ${error.message}`
        });
      }
    });

    // Debug endpoint
    this.app.get('/debug', async (req, res) => {
      try {
        if (!this.underdogService) {
          return res.json({ error: 'Service not initialized' });
        }

        const debugInfo = {
          sessionData: this.underdogService.sessionData,
          serviceStatus: this.getServiceStatus(),
          timestamp: new Date().toISOString()
        };

        res.json(debugInfo);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // Force location refresh endpoint
    this.app.post('/refresh-location', async (req, res) => {
      try {
        if (!this.underdogService) {
          return res.status(503).json({ 
            success: false, 
            error: 'Service not initialized' 
          });
        }

        console.log('🔄 Manual location refresh requested');
        const success = await this.underdogService.refreshLocation();
        
        res.json({
          success,
          message: success ? 'Location refresh successful' : 'Location refresh failed',
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.error('❌ Error during manual location refresh:', error);
        res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });
  }

  setupErrorHandling() {
    // 404 handler
    this.app.use((req, res) => {
      res.status(404).json({
        error: 'Endpoint not found',
        available_endpoints: [
          'GET /health',
          'POST /generate-links',
          'GET /debug',
          'POST /refresh-location'
        ]
      });
    });

    // Global error handler
    this.app.use((error, req, res, next) => {
      console.error('❌ Unhandled error:', error);
      res.status(500).json({
        error: 'Internal server error',
        message: error.message
      });
    });
  }

  getServiceStatus() {
    if (this.isInitializing) {
      return {
        httpCode: 200,
        response: {
          status: 'initializing',
          initialized: false,
          message: 'Service is starting up...'
        }
      };
    }

    if (this.initializationError) {
      return {
        httpCode: 503,
        response: {
          status: 'error',
          initialized: false,
          error: this.initializationError.message
        }
      };
    }

    if (!this.underdogService) {
      return {
        httpCode: 503,
        response: {
          status: 'not_initialized',
          initialized: false,
          message: 'Service not started'
        }
      };
    }

    const isReady = this.underdogService.sessionData.isLoggedIn;
    
    return {
      httpCode: isReady ? 200 : 503,
      response: {
        status: isReady ? 'ready' : 'not_ready',
        initialized: true,
        logged_in: this.underdogService.sessionData.isLoggedIn,
        location_verified: this.underdogService.sessionData.locationVerified,
        timestamp: new Date().toISOString()
      }
    };
  }

  async generateBetLink(betIds) {
    // Placeholder implementation
    // In actual implementation, this would:
    // 1. Use the authenticated session to make API calls
    // 2. Create a bet slip with the provided IDs
    // 3. Generate a shareable link
    
    console.log(`🎯 Generating bet link for IDs: ${betIds.join(', ')}`);
    
    // Mock implementation for testing
    const mockLink = `https://underdogfantasy.com/pick-em/share/${betIds.join('-')}`;
    
    // Simulate some processing time
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return mockLink;
  }

  async initializeService() {
    if (this.isInitializing) {
      console.log('⏳ Service initialization already in progress...');
      return;
    }

    this.isInitializing = true;
    this.initializationError = null;

    try {
      console.log('🚀 Starting enhanced Underdog service initialization...');

      const config = {
        headless: process.env.HEADLESS === 'true',
        proxy: process.env.PROXY_SERVER ? {
          server: process.env.PROXY_SERVER,
          username: process.env.PROXY_USERNAME,
          password: process.env.PROXY_PASSWORD,
          bypass: process.env.PROXY_BYPASS || '<-loopback>'
        } : null,
        credentials: {
          email: process.env.UNDERDOG_USERNAME,
          password: process.env.UNDERDOG_PASSWORD
        }
      };

      console.log('🔧 Configuration:', {
        headless: config.headless,
        hasProxy: !!config.proxy,
        hasCredentials: !!(config.credentials.email && config.credentials.password)
      });

      this.underdogService = new EnhancedUnderdogService(config);
      await this.underdogService.initialize();

      // Start session maintenance
      this.underdogService.maintainSession();

      console.log('✅ Enhanced service initialization completed successfully');
    } catch (error) {
      console.error('❌ Service initialization failed:', error);
      this.initializationError = error;
      
      // Clean up on failure
      if (this.underdogService) {
        await this.underdogService.shutdown();
        this.underdogService = null;
      }
    } finally {
      this.isInitializing = false;
    }
  }

  async start() {
    // Start HTTP server
    this.server = this.app.listen(this.port, () => {
      console.log(`🌐 Enhanced server running on port ${this.port}`);
      console.log(`📊 Health check: http://localhost:${this.port}/health`);
      console.log(`🎯 API endpoint: http://localhost:${this.port}/generate-links`);
    });

    // Initialize service in background
    this.initializeService().catch(error => {
      console.error('❌ Background initialization failed:', error);
    });

    // Graceful shutdown handling
    process.on('SIGINT', () => this.shutdown());
    process.on('SIGTERM', () => this.shutdown());
  }

  async shutdown() {
    console.log('🔄 Shutting down enhanced server...');

    if (this.server) {
      this.server.close();
    }

    if (this.underdogService) {
      await this.underdogService.shutdown();
    }

    console.log('✅ Enhanced server shutdown complete');
    process.exit(0);
  }
}

// Start the enhanced server
if (require.main === module) {
  const server = new EnhancedServer();
  server.start().catch(error => {
    console.error('❌ Failed to start enhanced server:', error);
    process.exit(1);
  });
}

module.exports = EnhancedServer;
