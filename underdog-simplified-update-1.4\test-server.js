const express = require('express');
require('dotenv').config();

console.log('Starting test server...');

const app = express();
const port = process.env.PORT || 3000;

app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  console.log('Health check requested');
  res.json({
    status: 'ready',
    initialized: true,
    message: 'Test server is running',
    timestamp: new Date().toISOString(),
    proxy_configured: !!process.env.PROXY_SERVER,
    credentials_configured: !!(process.env.UNDERDOG_USERNAME && process.env.UNDERDOG_PASSWORD)
  });
});

// Test endpoint for bet link generation
app.post('/generate-links', (req, res) => {
  console.log('Generate links requested with body:', req.body);
  const { betIds } = req.body;
  
  if (!betIds || !Array.isArray(betIds)) {
    return res.status(400).json({
      share_link: null,
      error: 'Invalid request: betIds array required'
    });
  }

  // Mock response for testing
  res.json({
    share_link: `https://underdogfantasy.com/pick-em/share/test-${betIds.join('-')}`,
    error: null,
    message: 'Test server - mock response',
    betIds: betIds
  });
});

// Start server
app.listen(port, () => {
  console.log(`✅ Test server running on port ${port}`);
  console.log(`📊 Health check: http://localhost:${port}/health`);
  console.log(`🎯 API endpoint: http://localhost:${port}/generate-links`);
  console.log(`🔧 Configuration:`);
  console.log(`   - Proxy configured: ${!!process.env.PROXY_SERVER}`);
  console.log(`   - Credentials configured: ${!!(process.env.UNDERDOG_USERNAME && process.env.UNDERDOG_PASSWORD)}`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🔄 Shutting down test server...');
  process.exit(0);
});

console.log('Test server setup complete');
