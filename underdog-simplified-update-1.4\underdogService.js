/**
 * Enhanced Underdog Fantasy Automation Service
 * Handles browser automation, login, and bet placement with proxy support and stealth measures
 */
const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const UserPreferencesPlugin = require('puppeteer-extra-plugin-user-preferences');
const axios = require('axios');
const { config } = require('./config');

// Configure stealth plugin
if (config.stealth.enabled) {
  const stealthPlugin = StealthPlugin();

  // Configure specific stealth features
  if (!config.stealth.removeWebdriver) {
    stealthPlugin.enabledEvasions.delete('navigator.webdriver');
  }
  if (!config.stealth.fakeUserAgent) {
    stealthPlugin.enabledEvasions.delete('user-agent-override');
  }
  if (!config.stealth.fakeViewport) {
    stealthPlugin.enabledEvasions.delete('navigator.webdriver');
  }
  if (!config.stealth.fakeLanguages) {
    stealthPlugin.enabledEvasions.delete('navigator.languages');
  }

  puppeteer.use(stealthPlugin);
  console.log('Stealth plugin enabled with configured evasions');
}

// Configure user preferences plugin for additional stealth
puppeteer.use(UserPreferencesPlugin({
  userPrefs: {
    // Disable various Chrome features that might reveal automation
    'profile.default_content_setting_values.notifications': 2,
    'profile.default_content_settings.popups': 0,
    'profile.managed_default_content_settings.images': 1,
    'profile.default_content_setting_values.plugins': 1,
    'profile.content_settings.plugin_whitelist.adobe-flash-player': 1,
    'profile.content_settings.exceptions.plugins.*,*.per_resource.adobe-flash-player': 1,
    'profile.default_content_setting_values.cookies': 1,
    'profile.default_content_setting_values.javascript': 1,
    'profile.default_content_setting_values.mixed_script': 1,
    'profile.default_content_setting_values.media_stream': 1,
    'profile.default_content_setting_values.geolocation': 1,
  }
}));

class UnderdogService {
  constructor() {
    this.browser = null;
    this.page = null;
    this.isInitialized = false;
    this.isLoggingIn = false;
    this.isRefreshingToken = false;
    this.isRefreshingLocation = false;
    this.refreshTimer = null;
    this.locationRefreshTimer = null;
    this.initializationAttempted = false;
    this.initializationSuccessful = false;
    this.lastLoginAttempt = null;
    this.lastTokenRefresh = null;
    this.lastLocationRefresh = null;

    // Session data
    this.sessionData = {
      isLoggedIn: false,
      authorization: null,
      encodedResponse: null,
      deviceId: null,
      cookies: null,
      cookieHeader: null,
      locationVerified: false,
    };
  }

  /**
   * Initialize the service and launch browser
   */
  async initialize() {
    try {
      console.log('Initializing Underdog service...');

      // Clear userDataDir before launching browser to ensure a clean state
      const fs = require('fs-extra');
      const path = require('path');
      const userDataDirPath = path.resolve(config.browser.userDataDir || './puppeteer_user_data');

      try {
        if (fs.existsSync(userDataDirPath)) {
          console.log(`Clearing user data directory: ${userDataDirPath}`);
          await fs.remove(userDataDirPath);
          console.log(`Successfully cleared user data directory: ${userDataDirPath}`);
        }
        fs.mkdirSync(userDataDirPath, { recursive: true });
        console.log(`Created fresh user data directory: ${userDataDirPath}`);
      } catch (dirError) {
        console.error(`Error managing user data directory ${userDataDirPath}:`, dirError);
        // Continue with initialization even if directory management fails
      }

      // Launch browser
      // Prepare enhanced browser arguments for stealth and proxy
      const args = [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--disable-gpu',
        '--no-first-run',
        '--no-zygote',
        '--window-size=1366,768',
        // Additional stealth arguments
        '--disable-blink-features=AutomationControlled',
        '--disable-features=VizDisplayCompositor',
        '--disable-ipc-flooding-protection',
        '--disable-renderer-backgrounding',
        '--disable-backgrounding-occluded-windows',
        '--disable-client-side-phishing-detection',
        '--disable-component-extensions-with-background-pages',
        '--disable-default-apps',
        '--disable-extensions',
        '--disable-features=TranslateUI',
        '--disable-hang-monitor',
        '--disable-prompt-on-repost',
        '--disable-sync',
        '--disable-web-security',
        '--metrics-recording-only',
        '--no-default-browser-check',
        '--no-pings',
        '--password-store=basic',
        '--use-mock-keychain',
        '--force-color-profile=srgb',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-features=TranslateUI,BlinkGenPropertyTrees',
        '--disable-domain-reliability',
        '--disable-background-networking',
      ];

      // Add proxy configuration if specified
      if (config.proxy && config.proxy.server) {
        console.log(`Configuring proxy server: ${config.proxy.server} (type: ${config.proxy.type})`);

        // For Puppeteer, use the simple format without protocol prefix
        // Authentication will be handled separately via page.authenticate()
        args.push(`--proxy-server=${config.proxy.server}`);
        console.log(`Proxy configuration applied: ${config.proxy.server}`);

        // Add additional proxy-related arguments for better compatibility
        args.push('--proxy-bypass-list=<-loopback>');
        args.push('--disable-web-security');
        args.push('--ignore-certificate-errors');
        args.push('--ignore-ssl-errors');
        args.push('--ignore-certificate-errors-spki-list');

        // For SOCKS proxies, add additional configuration
        if (config.proxy.type === 'socks4' || config.proxy.type === 'socks5') {
          args.push('--host-resolver-rules=MAP * ~NOTFOUND , EXCLUDE localhost');
        }
      }

      console.log('Launching browser with args:', args);

      this.browser = await puppeteer.launch({
        headless: config.browser.headless ? 'new' : false,
        userDataDir: userDataDirPath, // Use the path we just cleared
        args,
      });

      console.log('Browser launched successfully');

      // Test proxy connectivity immediately after browser launch
      if (config.proxy && config.proxy.server) {
        console.log('Testing proxy connectivity...');
        try {
          const testPage = await this.browser.newPage();

          // Set up proxy authentication for test page
          if (config.proxy.username && config.proxy.password) {
            await testPage.authenticate({
              username: config.proxy.username,
              password: config.proxy.password
            });
            console.log('Proxy authentication set up for test page');
          }

          // Test proxy by visiting ipinfo.io
          console.log('Navigating to ipinfo.io to test proxy...');
          await testPage.goto('https://ipinfo.io/json', { timeout: 30000 });
          const ipInfo = await testPage.evaluate(() => document.body.textContent);
          console.log('Proxy test result:', ipInfo);

          await testPage.close();
          console.log('Proxy connectivity test completed successfully');
        } catch (proxyTestError) {
          console.error('Proxy connectivity test failed:', proxyTestError.message);
          console.error('Proxy test error stack:', proxyTestError.stack);
          throw new Error(`Proxy test failed: ${proxyTestError.message}`);
        }
      }

      // CRITICAL: Grant geolocation permission at the browser context level
      const context = this.browser.defaultBrowserContext();
      await context.overridePermissions('https://underdogfantasy.com', ['geolocation']);
      console.log('Geolocation permission granted to Underdog Fantasy origin');

      // No need to set up additional proxy authentication since we included it in the args

      // Create a new page
      this.page = await this.browser.newPage();

      // Set up proxy authentication if credentials are provided
      if (config.proxy && config.proxy.server && config.proxy.username && config.proxy.password) {
        await this.page.authenticate({
          username: config.proxy.username,
          password: config.proxy.password
        });
        console.log('Proxy authentication configured');
      }

      // Add page error event listeners for debugging
      this.page.on('pageerror', error => {
        console.error(`[BrowserError] Page error: ${error.message}`);
      });

      this.page.on('console', msg => {
        if (msg.type() === 'error' || msg.type() === 'warning') {
          console.log(`[BrowserConsole] ${msg.type()}: ${msg.text()}`);
        }
      });

      // Enhanced stealth measures
      console.log('Applying enhanced stealth measures...');

      // Set realistic user agent
      await this.page.setUserAgent(
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36'
      );

      // Set realistic viewport
      await this.page.setViewport({
        width: 1366,
        height: 768,
        deviceScaleFactor: 1,
        hasTouch: false,
        isLandscape: true,
        isMobile: false,
      });

      // Remove webdriver property and other automation indicators
      await this.page.evaluateOnNewDocument(() => {
        // Remove webdriver property
        Object.defineProperty(navigator, 'webdriver', {
          get: () => undefined,
        });

        // Mock chrome runtime
        window.chrome = {
          runtime: {},
        };

        // Mock permissions
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) => (
          parameters.name === 'notifications' ?
            Promise.resolve({ state: Notification.permission }) :
            originalQuery(parameters)
        );

        // Mock plugins
        Object.defineProperty(navigator, 'plugins', {
          get: () => [1, 2, 3, 4, 5],
        });

        // Mock languages
        Object.defineProperty(navigator, 'languages', {
          get: () => ['en-US', 'en'],
        });
      });

      // Set geolocation to a US location (required for Underdog Fantasy)
      await this.page.setGeolocation({
        latitude: 40.7128, // New York City
        longitude: -74.0060,
        accuracy: 100
      });
      console.log('Set mock geolocation to New York City for US compliance');

      // Set timezone to match geolocation
      await this.page.emulateTimezone('America/New_York');

      // Set up request interception to capture auth tokens
      await this.page.setRequestInterception(true);

      this.page.on('request', this._interceptRequest.bind(this));

      // Add enhanced network response monitoring for token capture
      this.page.on('response', async (response) => {
        const url = response.url();

        // Monitor all Underdog-related responses for tokens
        if (url.includes('underdogfantasy.com') || url.includes('underdogsports.com') ||
            url.includes('/api/') || url.includes('auth') || url.includes('session') ||
            url.includes('login') || url.includes('location') || url.includes('geocomply')) {

          const status = response.status();
          const headers = response.headers();
          let responseData = '';

          try {
            // Try to get response text for JSON responses
            if (headers['content-type'] && headers['content-type'].includes('application/json')) {
              try {
                const respText = await response.text();
                responseData = respText.substring(0, 200) + (respText.length > 200 ? '...' : '');

                // Look for location tokens in response body
                if (respText.includes('location') || respText.includes('geocomply') || respText.includes('token')) {
                  try {
                    const jsonData = JSON.parse(respText);

                    // Check various possible token fields
                    const tokenFields = [
                      'user_location_token', 'userLocationToken', 'location_token', 'locationToken',
                      'geocomply_token', 'geocomplyToken', 'encoded_response', 'encodedResponse',
                      'verification_token', 'verificationToken', 'auth_token', 'authToken'
                    ];

                    for (const field of tokenFields) {
                      if (jsonData[field]) {
                        this.sessionData.encodedResponse = jsonData[field];
                        console.log(`[TokenCapture] Location token found in response body field '${field}': ${jsonData[field].substring(0, 50)}...`);
                        break;
                      }
                    }
                  } catch (parseError) {
                    // Not valid JSON, continue
                  }
                }
              } catch (e) {
                responseData = '<Failed to get response body>';
              }
            }
          } catch (err) {
            responseData = '<Error parsing response>';
          }

          // Check response headers for location tokens
          const locationTokenHeaders = [
            'user-location-token', 'x-user-location-token', 'location-token',
            'geocomply-token', 'x-location-token', 'x-geocomply-token'
          ];

          for (const headerName of locationTokenHeaders) {
            if (headers[headerName]) {
              this.sessionData.encodedResponse = headers[headerName];
              console.log(`[TokenCapture] Location token found in response header '${headerName}': ${headers[headerName].substring(0, 50)}...`);
              break;
            }
          }

          // Check cookies for location tokens
          if (headers['set-cookie']) {
            const cookies = Array.isArray(headers['set-cookie']) ? headers['set-cookie'] : [headers['set-cookie']];
            for (const cookie of cookies) {
              if (cookie.includes('location') || cookie.includes('geocomply') || cookie.includes('token')) {
                console.log(`[TokenCapture] Potential location token in cookie: ${cookie.substring(0, 100)}...`);

                // Extract token value from cookie
                const tokenMatch = cookie.match(/(location[_-]?token|geocomply[_-]?token|user[_-]?location[_-]?token)=([^;]+)/i);
                if (tokenMatch && tokenMatch[2]) {
                  this.sessionData.encodedResponse = tokenMatch[2];
                  console.log(`[TokenCapture] Location token extracted from cookie: ${tokenMatch[2].substring(0, 50)}...`);
                }
              }
            }
          }

          console.log(`[NetworkMonitor] Response: ${url.substring(0, 100)} - Status: ${status}`, {
            headers: {
              'set-cookie': headers['set-cookie'] ? 'Present' : 'None',
              'authorization': headers['authorization'] ? 'Present' : 'None',
            },
            responsePreview: responseData
          });
        }
      });

      // Log in - CRITICAL: This should be the ONLY login attempt during initialization
      const loginSuccess = await this.login();

      if (!loginSuccess) {
        console.error('CRITICAL FAILURE: Failed to log in to Underdog Fantasy during initialization');
        throw new Error('Login failed during initialization');
      }

      this.isInitialized = true;
      console.log('CRITICAL SUCCESS: Underdog service initialized and logged in successfully');
      return true;
    } catch (error) {
      console.error('CRITICAL FAILURE: Failed to initialize Underdog service:', error);
      this.isInitialized = false;

      // Clean up resources on failure
      if (this.browser) {
        try {
          await this.browser.close();
          console.log('Browser closed after initialization failure');
        } catch (closeError) {
          console.error('Error closing browser after initialization failure:', closeError);
        }
        this.browser = null;
        this.page = null;
      }

      throw error; // Re-throw to signal initialization failure
    }
  }

  /**
   * Check JavaScript variables and localStorage for location tokens
   */
  async _checkJavaScriptForTokens() {
    try {
      console.log('[TokenCapture] Checking JavaScript variables for location tokens...');

      const tokenData = await this.page.evaluate(() => {
        const results = {
          windowVars: {},
          localStorage: {},
          sessionStorage: {},
          cookies: document.cookie
        };

        // Check common window variables where tokens might be stored
        const windowVarNames = [
          'userLocationToken', 'user_location_token', 'locationToken', 'location_token',
          'geocomplyToken', 'geocomply_token', 'encodedResponse', 'encoded_response',
          'authToken', 'auth_token', 'verificationToken', 'verification_token',
          'geoToken', 'geo_token', 'udToken', 'ud_token'
        ];

        for (const varName of windowVarNames) {
          if (window[varName]) {
            results.windowVars[varName] = window[varName];
          }
        }

        // Check localStorage
        try {
          for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && (key.includes('token') || key.includes('location') || key.includes('geocomply'))) {
              results.localStorage[key] = localStorage.getItem(key);
            }
          }
        } catch (e) {
          results.localStorage.error = e.message;
        }

        // Check sessionStorage
        try {
          for (let i = 0; i < sessionStorage.length; i++) {
            const key = sessionStorage.key(i);
            if (key && (key.includes('token') || key.includes('location') || key.includes('geocomply'))) {
              results.sessionStorage[key] = sessionStorage.getItem(key);
            }
          }
        } catch (e) {
          results.sessionStorage.error = e.message;
        }

        // Check for GeoComply specific objects
        if (window.GeoComply) {
          results.geoComplyObject = 'Present';
        }

        // Check for any global variables containing 'token'
        const globalTokenVars = {};
        for (const prop in window) {
          if (prop.toLowerCase().includes('token') && typeof window[prop] === 'string') {
            globalTokenVars[prop] = window[prop];
          }
        }
        results.globalTokenVars = globalTokenVars;

        return results;
      });

      console.log('[TokenCapture] JavaScript token search results:', tokenData);

      // Extract any found tokens
      for (const [varName, value] of Object.entries(tokenData.windowVars)) {
        if (value && typeof value === 'string' && value.length > 10) {
          this.sessionData.encodedResponse = value;
          console.log(`[TokenCapture] Location token found in window.${varName}: ${value.substring(0, 50)}...`);
          return;
        }
      }

      for (const [key, value] of Object.entries(tokenData.localStorage)) {
        if (value && typeof value === 'string' && value.length > 10) {
          this.sessionData.encodedResponse = value;
          console.log(`[TokenCapture] Location token found in localStorage.${key}: ${value.substring(0, 50)}...`);
          return;
        }
      }

      for (const [key, value] of Object.entries(tokenData.sessionStorage)) {
        if (value && typeof value === 'string' && value.length > 10) {
          this.sessionData.encodedResponse = value;
          console.log(`[TokenCapture] Location token found in sessionStorage.${key}: ${value.substring(0, 50)}...`);
          return;
        }
      }

    } catch (error) {
      console.error('[TokenCapture] Error checking JavaScript for tokens:', error);
    }
  }

  /**
   * Intercept requests to capture auth tokens
   */
  _interceptRequest(request) {
    try {
      const url = request.url();
      const headers = request.headers() || {};

      // Capture authorization token from any Underdog-related requests
      if (url.includes('underdogfantasy.com') || url.includes('underdogsports.com')) {
        // Look for authorization header
        if (headers.authorization) {
          this.sessionData.authorization = headers.authorization;
          console.log(`[TokenCapture] Authorization token captured from: ${url.substring(0, 50)}...`);
        }

        // Look for device ID
        if (headers['client-device-id']) {
          this.sessionData.deviceId = headers['client-device-id'];
          console.log(`[TokenCapture] Device ID captured: ${headers['client-device-id']}`);
        }

        // Look for location token in various header formats
        const locationTokenHeaders = [
          'user-location-token',
          'x-user-location-token',
          'location-token',
          'geocomply-token',
          'x-location-token',
          'x-geocomply-token'
        ];

        for (const headerName of locationTokenHeaders) {
          if (headers[headerName]) {
            this.sessionData.encodedResponse = headers[headerName];
            console.log(`[TokenCapture] Location token captured from header '${headerName}': ${headers[headerName].substring(0, 50)}...`);
            break;
          }
        }

        // Log all headers for debugging if we haven't found the location token yet
        if (!this.sessionData.encodedResponse && url.includes('/api/')) {
          console.log(`[TokenCapture] Headers from ${url}:`, Object.keys(headers));
        }
      }

      // Continue the request
      request.continue();
    } catch (error) {
      console.error('Error in request interception:', error);
      request.continue();
    }
  }

  /**
   * Log in to Underdog Fantasy with retry logic and enhanced error handling
   * @param {boolean} isRefresh - Whether this is a refresh login or initial login
   * @returns {Promise<boolean>} Whether login was successful
   */
  async login(isRefresh = false) {
    // Prevent multiple login attempts
    if (this.isLoggingIn) {
      console.log('Login already in progress, skipping duplicate request');
      return false;
    }

    this.isLoggingIn = true;
    this.lastLoginAttempt = Date.now();

    const maxAttempts = config.retry.maxLoginAttempts;
    let lastError = null;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        console.log(`${isRefresh ? 'Refreshing login' : 'Logging in'} to Underdog Fantasy (attempt ${attempt}/${maxAttempts})...`);

        const success = await this._performLogin(isRefresh, attempt);
        if (success) {
          console.log(`Login successful on attempt ${attempt}`);
          this.isLoggingIn = false;
          return true;
        }

        // If this wasn't the last attempt, wait before retrying
        if (attempt < maxAttempts) {
          console.log(`Login attempt ${attempt} failed, waiting ${config.retry.loginRetryDelay}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, config.retry.loginRetryDelay));
        }
      } catch (error) {
        lastError = error;
        console.error(`Login attempt ${attempt} failed with error:`, error.message);
        console.error(`Full error details:`, error);
        console.error(`Error stack:`, error.stack);

        // Take a detailed screenshot of the failure
        try {
          const errorScreenshotPath = `login-error-attempt-${attempt}-${Date.now()}.png`;
          await this.page.screenshot({ path: errorScreenshotPath, fullPage: true });
          console.log(`Saved error screenshot: ${errorScreenshotPath}`);

          // Also save the HTML content
          const htmlContent = await this.page.content();
          const fs = require('fs');
          const htmlPath = `login-error-attempt-${attempt}-${Date.now()}.html`;
          fs.writeFileSync(htmlPath, htmlContent);
          console.log(`Saved error HTML: ${htmlPath}`);

          // Get the current URL
          const currentUrl = this.page.url();
          console.log(`Current URL during error: ${currentUrl}`);
        } catch (debugError) {
          console.error('Failed to capture error debug info:', debugError);
        }

        // Check for specific errors that indicate we should not retry
        if (error.message.includes('user_dismiss') ||
            error.message.includes('developer mode') ||
            error.message.includes('Protocol error') ||
            error.message.includes('ERR_NO_SUPPORTED_PROXIES') ||
            error.message.includes('ERR_PROXY_')) {
          console.error('Encountered non-retryable error, stopping login attempts');
          break;
        }

        // If this wasn't the last attempt, wait before retrying
        if (attempt < maxAttempts) {
          console.log(`Waiting ${config.retry.loginRetryDelay}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, config.retry.loginRetryDelay));
        }
      }
    }

    console.error(`All ${maxAttempts} login attempts failed`);
    this.isLoggingIn = false;
    return false;
  }

  /**
   * Perform a single login attempt
   * @param {boolean} isRefresh - Whether this is a refresh login or initial login
   * @param {number} attempt - Current attempt number
   * @returns {Promise<boolean>} Whether login was successful
   */
  async _performLogin(isRefresh, attempt) {

    try {
      // Take a screenshot before login attempt for debugging
      if (!config.browser.headless) {
        try {
          const screenshotPath = `login-before-attempt-${attempt}-${Date.now()}.png`;
          await this.page.screenshot({ path: screenshotPath, fullPage: true });
          console.log(`Saved pre-login screenshot to ${screenshotPath}`);
        } catch (screenshotError) {
          console.error('Failed to take pre-login screenshot:', screenshotError);
        }
      }

      // Add random delay to appear more human-like
      const humanDelay = Math.random() * 2000 + 1000; // 1-3 seconds
      console.log(`Adding human-like delay of ${Math.round(humanDelay)}ms before navigation...`);
      await new Promise(resolve => setTimeout(resolve, humanDelay));

      // Reset session data if this is not a refresh
      if (!isRefresh) {
        this.sessionData = {
          isLoggedIn: false,
          authorization: null,
          encodedResponse: null,
          deviceId: null,
          cookies: null,
          cookieHeader: null,
        };
      }

      // Navigate to login page
      console.log(`Navigating to ${config.endpoints.underdogLogin}...`);

      // Set up response monitoring for developer mode detection
      let developerModeDetected = false;
      const responseHandler = async (response) => {
        if (response.url().includes('api.underdogfantasy.com')) {
          try {
            const responseText = await response.text();
            if (responseText.includes('user_dismiss') && responseText.includes('developer mode')) {
              console.error('DEVELOPER MODE DETECTED: Underdog Fantasy is rejecting requests due to developer mode detection');
              developerModeDetected = true;
            }
          } catch (e) {
            // Ignore errors reading response text
          }
        }
      };

      this.page.on('response', responseHandler);

      try {
        await this.page.goto(config.endpoints.underdogLogin, {
          waitUntil: 'networkidle2',
          timeout: config.timeouts.navigation,
        });
      } finally {
        this.page.off('response', responseHandler);
      }

      // Check if developer mode was detected during navigation
      if (developerModeDetected) {
        throw new Error('Developer mode detected by Underdog Fantasy. Please disable developer tools and try again.');
      }

      // Check if already logged in
      console.log(`Checking for success indicator: ${config.selectors.login.successIndicator}`);
      const alreadyLoggedIn = await this.page.$(config.selectors.login.successIndicator);

      if (alreadyLoggedIn) {
        console.log('Already logged in, skipping login form');
        this.sessionData.isLoggedIn = true;

        // Collect cookies
        this.sessionData.cookies = await this.page.cookies();
        this.sessionData.cookieHeader = this._formatCookieHeader(this.sessionData.cookies);

        // Navigate to pick-em page to trigger API requests that contain tokens
        if (!this.sessionData.authorization || !this.sessionData.encodedResponse) {
          console.log('Already logged in but missing tokens, navigating to pick-em page to capture tokens...');
          await this.page.goto(config.endpoints.underdogPickem, {
            waitUntil: 'networkidle2',
            timeout: config.timeouts.navigation,
          });

          // Wait for tokens with timeout
          const startTime = Date.now();
          const tokenTimeout = 20000; // 20 seconds

          while (
            (!this.sessionData.authorization || !this.sessionData.encodedResponse) &&
            Date.now() - startTime < tokenTimeout
          ) {
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        }

        // Check if we have all required tokens
        if (!this.sessionData.authorization || !this.sessionData.encodedResponse) {
          console.error('Already logged in but failed to capture authentication tokens');

          // Try to dump HTML for debugging
          try {
            const html = await this.page.content();
            console.log('Current page HTML:', html.substring(0, 500) + '...');
          } catch (htmlError) {
            console.error('Failed to get page HTML:', htmlError);
          }

          this.isLoggingIn = false;
          return false;
        }

        // Mark location as verified since we just logged in
        this.sessionData.locationVerified = true;
        this.lastLocationRefresh = Date.now();

        // Start session refresh and location refresh timers
        this._startSessionRefreshTimer();
        this._startLocationRefreshTimer();

        this.isLoggingIn = false;
        return true;
      }

      // Not already logged in, need to fill login form
      console.log('Not logged in, filling login form...');

      // Wait for email input with visible: true to ensure it's interactive
      console.log(`Waiting for email input: ${config.selectors.login.emailInput}`);
      await this.page.waitForSelector(config.selectors.login.emailInput, {
        visible: true,
        timeout: config.timeouts.login,
      });

      // Type email and password with human-like behavior
      console.log('Typing email with human-like behavior...');
      console.log(`Email to type: "${config.credentials.username}"`);

      // Clear the email field first
      await this.page.click(config.selectors.login.emailInput);
      await this.page.keyboard.down('Control');
      await this.page.keyboard.press('KeyA');
      await this.page.keyboard.up('Control');
      await this.page.keyboard.press('Delete');

      await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 200)); // Random delay
      await this.page.type(config.selectors.login.emailInput, config.credentials.username, {
        delay: Math.random() * 100 + 50 // Random typing speed
      });

      // Take screenshot after email entry
      try {
        const screenshotPath = `email-typed-${Date.now()}.png`;
        await this.page.screenshot({ path: screenshotPath, fullPage: true });
        console.log(`Screenshot after email entry: ${screenshotPath}`);
      } catch (screenshotError) {
        console.error('Failed to take email screenshot:', screenshotError);
      }

      console.log('Typing password with human-like behavior...');
      console.log(`Password to type: "${config.credentials.password}" (length: ${config.credentials.password.length})`);
      console.log(`Password characters: ${config.credentials.password.split('').map(c => `'${c}'`).join(', ')}`);

      // Clear the password field first
      await this.page.click(config.selectors.login.passwordInput);
      await this.page.keyboard.down('Control');
      await this.page.keyboard.press('KeyA');
      await this.page.keyboard.up('Control');
      await this.page.keyboard.press('Delete');

      await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 200)); // Random delay

      // Type password character by character for better control
      for (let i = 0; i < config.credentials.password.length; i++) {
        const char = config.credentials.password[i];
        console.log(`Typing character ${i + 1}/${config.credentials.password.length}: '${char}'`);
        await this.page.keyboard.type(char);
        await new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 50));
      }

      // Take screenshot after password entry
      try {
        const screenshotPath = `password-typed-${Date.now()}.png`;
        await this.page.screenshot({ path: screenshotPath, fullPage: true });
        console.log(`Screenshot after password entry: ${screenshotPath}`);
      } catch (screenshotError) {
        console.error('Failed to take password screenshot:', screenshotError);
      }

      // Verify what was actually typed in the password field
      try {
        const typedPassword = await this.page.$eval(config.selectors.login.passwordInput, el => el.value);
        console.log(`Password field contains: "${typedPassword}" (length: ${typedPassword.length})`);
        console.log(`Expected: "${config.credentials.password}" (length: ${config.credentials.password.length})`);
        console.log(`Match: ${typedPassword === config.credentials.password}`);
      } catch (evalError) {
        console.error('Failed to read password field value:', evalError);
      }

      // Add a small delay before submitting (human-like behavior)
      await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 500));

      // Take a screenshot before clicking submit for debugging
      try {
        const screenshotPath = `login-form-filled-${Date.now()}.png`;
        await this.page.screenshot({ path: screenshotPath, fullPage: true });
        console.log(`Saved form-filled screenshot to ${screenshotPath}`);

        // Also save the HTML content for debugging
        const htmlContent = await this.page.content();
        const fs = require('fs');
        const htmlPath = `login-form-filled-${Date.now()}.html`;
        fs.writeFileSync(htmlPath, htmlContent);
        console.log(`Saved form HTML to ${htmlPath}`);
      } catch (screenshotError) {
        console.error('Failed to take form-filled screenshot:', screenshotError);
      }

      // Submit form and wait for navigation
      console.log(`Clicking submit button: ${config.selectors.login.submitButton}`);
      await Promise.all([
        this.page.waitForNavigation({
          waitUntil: 'networkidle2',
          timeout: config.timeouts.navigation,
        }),
        this.page.click(config.selectors.login.submitButton),
      ]);

      // Check for error message
      console.log(`Checking for error message: ${config.selectors.login.errorMessage}`);
      const errorElement = await this.page.$(config.selectors.login.errorMessage);
      if (errorElement) {
        const errorText = await this.page.evaluate(el => el.textContent, errorElement);
        console.error('Login error:', errorText);

        // Take a screenshot of the error for debugging
        if (!config.browser.headless) {
          try {
            const screenshotPath = `login-error-${Date.now()}.png`;
            await this.page.screenshot({ path: screenshotPath });
            console.log(`Saved error screenshot to ${screenshotPath}`);
          } catch (screenshotError) {
            console.error('Failed to take error screenshot:', screenshotError);
          }
        }

        this.isLoggingIn = false;
        return false;
      }

      // Wait for success indicator
      console.log(`Waiting for success indicator: ${config.selectors.login.successIndicator}`);
      await this.page.waitForSelector(config.selectors.login.successIndicator, {
        visible: true,
        timeout: config.timeouts.navigation,
      });

      console.log('Login successful, collecting cookies...');

      // Collect cookies
      this.sessionData.cookies = await this.page.cookies();
      this.sessionData.cookieHeader = this._formatCookieHeader(this.sessionData.cookies);

      // Wait for auth tokens to be captured
      console.log('Waiting for auth tokens to be captured...');

      // Navigate to pick-em page to trigger API requests that contain tokens
      await this.page.goto(config.endpoints.underdogPickem, {
        waitUntil: 'networkidle2',
        timeout: config.timeouts.navigation,
      });

      // Try to trigger GeoComply location verification by navigating to specific pages
      console.log('Triggering GeoComply location verification...');

      // Navigate to lobby page which often triggers location verification
      try {
        await this.page.goto(config.endpoints.underdogLobby, {
          waitUntil: 'networkidle2',
          timeout: config.timeouts.navigation,
        });
        console.log('Navigated to lobby page for location verification');

        // Wait a bit for GeoComply to load
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Check for location token in JavaScript variables
        await this._checkJavaScriptForTokens();

      } catch (lobbyError) {
        console.log('Lobby navigation failed, trying alternative pages:', lobbyError.message);
      }

      // Try navigating to bet placement page which definitely requires location verification
      try {
        await this.page.goto('https://underdogfantasy.com/pick-em/higher-lower/all/home', {
          waitUntil: 'networkidle2',
          timeout: config.timeouts.navigation,
        });
        console.log('Navigated to bet placement page for location verification');

        // Wait for GeoComply to potentially load
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Check for location token in JavaScript variables again
        await this._checkJavaScriptForTokens();

      } catch (betPageError) {
        console.log('Bet page navigation failed:', betPageError.message);
      }

      // Wait for tokens with extended timeout and additional verification attempts
      const startTime = Date.now();
      const tokenTimeout = 45000; // Extended to 45 seconds
      let lastTokenCheck = 0;

      while (
        (!this.sessionData.authorization || !this.sessionData.encodedResponse || !this.sessionData.deviceId) &&
        Date.now() - startTime < tokenTimeout
      ) {
        const elapsed = Date.now() - startTime;
        console.log('Waiting for tokens...', {
          authorization: !!this.sessionData.authorization,
          encodedResponse: !!this.sessionData.encodedResponse,
          deviceId: !!this.sessionData.deviceId,
          elapsed: elapsed
        });

        // Every 10 seconds, try additional location verification triggers
        if (elapsed - lastTokenCheck > 10000) {
          lastTokenCheck = elapsed;
          console.log('[TokenCapture] Attempting additional location verification triggers...');

          try {
            // Try clicking on elements that might trigger location verification
            await this.page.evaluate(() => {
              // Look for and click any location-related buttons
              const locationButtons = document.querySelectorAll('[data-testid*="location"], [class*="location"], [id*="location"]');
              locationButtons.forEach(btn => {
                if (btn.click) btn.click();
              });

              // Try to trigger any GeoComply functions if they exist
              if (window.GeoComply && window.GeoComply.verify) {
                try {
                  window.GeoComply.verify();
                } catch (e) {
                  console.log('GeoComply.verify() failed:', e);
                }
              }

              // Dispatch location-related events
              window.dispatchEvent(new Event('focus'));
              window.dispatchEvent(new Event('click'));
            });

            // Check JavaScript variables again
            await this._checkJavaScriptForTokens();

            // Try refreshing the current page to trigger location verification
            if (elapsed > 20000 && !this.sessionData.encodedResponse) {
              console.log('[TokenCapture] Refreshing page to trigger location verification...');
              await this.page.reload({ waitUntil: 'networkidle2' });
              await new Promise(resolve => setTimeout(resolve, 3000));
              await this._checkJavaScriptForTokens();
            }

          } catch (triggerError) {
            console.log('[TokenCapture] Error during additional verification triggers:', triggerError.message);
          }
        }

        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // Check if we have all required tokens
      if (!this.sessionData.authorization || !this.sessionData.encodedResponse) {
        console.error('Failed to capture authentication tokens');

        // Take a screenshot for debugging
        if (!config.browser.headless) {
          try {
            const screenshotPath = `token-capture-failed-${Date.now()}.png`;
            await this.page.screenshot({ path: screenshotPath, fullPage: true });
            console.log(`Saved token capture failure screenshot to ${screenshotPath}`);
          } catch (screenshotError) {
            console.error('Failed to take token capture failure screenshot:', screenshotError);
          }
        }

        this.isLoggingIn = false;
        return false;
      }

      console.log('Auth tokens captured successfully');
      this.sessionData.isLoggedIn = true;

      // Mark location as verified since we just logged in
      this.sessionData.locationVerified = true;
      this.lastLocationRefresh = Date.now();

      // Start session refresh and location refresh timers
      this._startSessionRefreshTimer();
      this._startLocationRefreshTimer();

      this.isLoggingIn = false;
      return true;
    } catch (error) {
      console.error('Login failed:', error);

      // Take a screenshot of the failure for debugging
      if (!config.browser.headless && this.page) {
        try {
          const screenshotPath = `login-exception-${Date.now()}.png`;
          await this.page.screenshot({ path: screenshotPath, fullPage: true });
          console.log(`Saved exception screenshot to ${screenshotPath}`);

          // Also dump HTML
          const htmlPath = `login-exception-${Date.now()}.html`;
          const html = await this.page.content();
          require('fs').writeFileSync(htmlPath, html);
          console.log(`Saved page HTML to ${htmlPath}`);
        } catch (screenshotError) {
          console.error('Failed to take exception screenshot:', screenshotError);
        }
      }

      this.isLoggingIn = false;
      return false;
    }
  }

  /**
   * Format cookies into a header string
   */
  _formatCookieHeader(cookies) {
    if (!cookies || !Array.isArray(cookies)) {
      return '';
    }

    return cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');
  }

  /**
   * Start the session refresh timer
   */
  _startSessionRefreshTimer() {
    // Clear any existing timer
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }

    // Set up a new timer
    this.refreshTimer = setInterval(
      this._refreshSession.bind(this),
      config.timeouts.sessionRefresh
    );

    console.log(`Session refresh timer started (${config.timeouts.sessionRefresh / 1000 / 60} minutes)`);
  }

  /**
   * Start the location refresh timer
   */
  _startLocationRefreshTimer() {
    if (this.locationRefreshTimer) {
      clearInterval(this.locationRefreshTimer);
    }

    // Start with the configured interval
    let currentInterval = config.refresh.locationInterval;
    let failureCount = 0;
    const maxFailures = 3;

    console.log(`Location refresh timer started (${currentInterval / 60000} minutes)`);

    this.locationRefreshTimer = setInterval(async () => {
      // Check browser health before refresh
      const browserHealthy = await this._checkBrowserHealth();
      if (!browserHealthy) {
        console.log('[LocationRefresh] Browser health check failed, attempting recovery...');
        // Clear the current timer
        clearInterval(this.locationRefreshTimer);
        this.locationRefreshTimer = null;

        // Attempt full reinitialization
        await this.shutdown();
        await this.initialize();
        const loginSuccess = await this.login(false);
        console.log(`[LocationRefresh] Recovery initialization: ${loginSuccess ? 'successful' : 'failed'}`);

        // Restart timer
        if (loginSuccess) {
          this._startLocationRefreshTimer();
        }
        return;
      }

      const beforeStatus = this.sessionData.locationVerified;
      await this._refreshLocation();
      const afterStatus = this.sessionData.locationVerified;

      // Track failures and adjust interval if needed
      if (beforeStatus && !afterStatus) {
        failureCount++;
        console.log(`[LocationRefresh] Failure detected (${failureCount}/${maxFailures})`);

        if (failureCount >= maxFailures) {
          console.log('[LocationRefresh] Multiple failures detected, attempting recovery...');

          // Clear the current timer
          clearInterval(this.locationRefreshTimer);
          this.locationRefreshTimer = null;

          // Attempt full login
          const loginSuccess = await this.login(true);
          console.log(`[LocationRefresh] Recovery login attempt: ${loginSuccess ? 'successful' : 'failed'}`);

          // Restart timer with fresh interval
          failureCount = 0;
          this._startLocationRefreshTimer();
          return;
        }
      } else if (afterStatus) {
        // Reset failure count on success
        failureCount = 0;
      }
    }, currentInterval);
  }

  /**
   * Check browser health status
   */
  async _checkBrowserHealth() {
    try {
      console.log('[BrowserHealth] Checking browser instance status...');

      if (!this.browser || !this.page) {
        console.error('[BrowserHealth] Browser or page instance is null');
        return false;
      }

      // Check if browser is connected
      const connected = this.browser.isConnected();
      console.log(`[BrowserHealth] Browser connected: ${connected}`);

      if (!connected) {
        return false;
      }

      // Check page status
      const pageStatus = {
        isClosed: this.page.isClosed ? this.page.isClosed() : 'Unknown',
        url: await this.page.evaluate(() => window.location.href).catch(() => 'Error getting URL')
      };

      console.log('[BrowserHealth] Page status:', JSON.stringify(pageStatus, null, 2));

      // Check memory usage if possible
      try {
        const metrics = await this.page.metrics();
        console.log('[BrowserHealth] Browser metrics:', {
          JSHeapUsedSize: `${Math.round(metrics.JSHeapUsedSize / (1024 * 1024))}MB`,
          JSHeapTotalSize: `${Math.round(metrics.JSHeapTotalSize / (1024 * 1024))}MB`,
          Nodes: metrics.Nodes,
          ScriptDuration: `${Math.round(metrics.ScriptDuration * 1000)}ms`
        });
      } catch (metricsError) {
        console.log('[BrowserHealth] Could not retrieve browser metrics:', metricsError.message);
      }

      return connected && !pageStatus.isClosed;
    } catch (error) {
      console.error('[BrowserHealth] Error checking browser health:', error);
      return false;
    }
  }

  /**
   * More comprehensive login verification
   */
  async _verifyLoginState() {
    // Check multiple indicators of logged-in state
    const stateChecks = await this.page.evaluate(() => {
      return {
        // Check URL (should not be on login page)
        isOnLoginPage: window.location.href.includes('/login'),

        // Check for known logged-in UI elements (multiple selectors for redundancy)
        hasUserProfile: !!document.querySelector('.profile-icon, .user-avatar, .account-menu'),
        hasLogoutOption: !!document.querySelector('[data-test="logout"], .logout-button, button:contains("Log out")'),

        // Check for login form (should not be present if logged in)
        hasLoginForm: !!document.querySelector('form[action*="login"], [data-test="login-form"]'),

        // Look for balance display (typically only shown when logged in)
        hasBalanceDisplay: !!document.querySelector('.balance, .wallet-balance, .account-funds'),

        // Check page title for login-related terms
        pageTitle: document.title
      };
    });

    console.log('[StateValidation] Login state verification:', JSON.stringify(stateChecks, null, 2));

    // Calculate confidence score for login status
    const positiveIndicators = [
      !stateChecks.isOnLoginPage,
      stateChecks.hasUserProfile,
      stateChecks.hasLogoutOption,
      !stateChecks.hasLoginForm,
      stateChecks.hasBalanceDisplay
    ].filter(Boolean).length;

    const totalIndicators = 5;
    const loginConfidence = (positiveIndicators / totalIndicators) * 100;

    console.log(`[StateValidation] Login confidence: ${loginConfidence}% (${positiveIndicators}/${totalIndicators} indicators)`);

    // Return both the raw checks and the confidence score
    return {
      checks: stateChecks,
      confidence: loginConfidence,
      isLoggedIn: loginConfidence > 60 // Consider logged in if confidence is above 60%
    };
  }

  /**
   * Refresh the session
   */
  async _refreshSession() {
    // Prevent concurrent refresh attempts
    if (this.isRefreshingToken) {
      console.log('Session refresh already in progress, skipping');
      return;
    }

    this.isRefreshingToken = true;
    this.lastTokenRefresh = Date.now();

    try {
      console.log('Refreshing session...');

      // First try a lightweight check - just navigate to pick-em page
      await this.page.goto(config.endpoints.underdogPickem, {
        waitUntil: 'networkidle2',
        timeout: config.timeouts.navigation,
      });

      // Check if still logged in
      const loggedIn = await this.page.$(config.selectors.login.successIndicator);

      if (!loggedIn) {
        console.log('Session expired during refresh, performing full login...');
        const loginSuccess = await this.login(true); // true = isRefresh

        if (!loginSuccess) {
          console.error('Failed to refresh session with full login');
          this.sessionData.isLoggedIn = false;
          this.sessionData.locationVerified = false;
        } else {
          console.log('Session refreshed successfully with full login');
          // Login already sets locationVerified to true and starts the location refresh timer
        }
      } else {
        // We're still logged in, but let's check if we still have valid tokens
        // by making a lightweight API call
        try {
          // Refresh tokens by navigating to trigger API requests
          console.log('Still logged in, refreshing tokens...');

          // Collect fresh cookies
          this.sessionData.cookies = await this.page.cookies();
          this.sessionData.cookieHeader = this._formatCookieHeader(this.sessionData.cookies);

          console.log('Session refreshed successfully');
        } catch (tokenError) {
          console.error('Error refreshing tokens:', tokenError);

          // If token refresh failed but we're still logged in, we can continue
          // The next API call might fail, but we'll let that happen and handle it there
        }
      }
    } catch (error) {
      console.error('Failed to refresh session:', error);

      // Take a screenshot for debugging
      if (!config.browser.headless && this.page) {
        try {
          const screenshotPath = `refresh-error-${Date.now()}.png`;
          await this.page.screenshot({ path: screenshotPath, fullPage: true });
          console.log(`Saved refresh error screenshot to ${screenshotPath}`);
        } catch (screenshotError) {
          console.error('Failed to take refresh error screenshot:', screenshotError);
        }
      }
    } finally {
      this.isRefreshingToken = false;
    }
  }

  /**
   * Refresh the location verification
   * This is a separate method from session refresh because location verification
   * needs to happen more frequently than general session refresh
   */
  async _refreshLocation() {
    // Prevent concurrent refresh attempts
    if (this.isRefreshingLocation || this.isRefreshingToken || this.isLoggingIn) {
      console.log('Location refresh skipped: another refresh operation is in progress');
      return;
    }

    this.isRefreshingLocation = true;
    this.lastLocationRefresh = Date.now();

    // Track token ages and correlate with failures
    const tokenAge = this.lastTokenRefresh ? (Date.now() - this.lastTokenRefresh) / 1000 : null;
    const loginAge = this.lastLoginAttempt ? (Date.now() - this.lastLoginAttempt) / 1000 : null;

    console.log('[LocationRefresh] Starting with token age:', {
      tokenAgeSeconds: tokenAge,
      loginAgeSeconds: loginAge,
      isLoggedIn: this.sessionData.isLoggedIn,
      hasAuthToken: !!this.sessionData.authorization,
      hasLocationToken: !!this.sessionData.encodedResponse
    });

    try {
      console.log('[LocationRefresh] Starting proactive location verification...');

      // Check if we're logged in or if page is available
      if (!this.sessionData.isLoggedIn || !this.page || (this.page.isClosed && this.page.isClosed())) {
        console.log('[LocationRefresh] Not logged in or page is closed, cannot refresh location');
        this.sessionData.locationVerified = false;
        return;
      }

      // Log cookies BEFORE navigation
      const beforeCookies = await this.page.cookies();
      console.log('[LocationRefresh] Cookies BEFORE navigation:', JSON.stringify({
        cookieCount: beforeCookies.length,
        sessionCookies: beforeCookies.filter(c => !c.expires || c.expires === -1).length,
        authCookies: beforeCookies.filter(c =>
          c.name.toLowerCase().includes('auth') ||
          c.name.toLowerCase().includes('token') ||
          c.name.toLowerCase().includes('session')
        ).map(c => ({ name: c.name, expires: c.expires ? new Date(c.expires * 1000).toISOString() : 'session' }))
      }, null, 2));

      // Monitor for JavaScript errors during navigation
      const pageErrors = [];
      const consoleListener = msg => {
        if (msg.type() === 'error') {
          pageErrors.push(msg.text());
        }
      };

      this.page.on('console', consoleListener);

      // Navigate to the lobby page to trigger location verification
      console.log(`[LocationRefresh] Navigating to ${config.endpoints.underdogLobby} for location verification...`);
      await this.page.goto(config.endpoints.underdogLobby, {
        waitUntil: 'networkidle2',
        timeout: config.timeouts.navigation,
      });

      // Remove the listener after navigation
      this.page.off('console', consoleListener);

      if (pageErrors.length > 0) {
        console.log('[LocationRefresh] JavaScript errors during navigation:', pageErrors);
      }

      // CHECKLIST ITEM 1.1: Post-Navigation URL Check
      const currentUrl = await this.page.url();
      console.log(`[RefreshNavCheck] Current URL after keep-alive navigation: ${currentUrl}`);

      // Check if we've been redirected to a login page
      const isRedirectedToLogin = currentUrl.includes('/login') ||
                               currentUrl.includes('/sign-in') ||
                               currentUrl.includes('/auth');

      if (isRedirectedToLogin) {
        console.warn('[RefreshNavCheck] Detected redirect to login page during refresh');
      }

      // Log cookies AFTER navigation
      const afterCookies = await this.page.cookies();
      console.log('[LocationRefresh] Cookies AFTER navigation:', JSON.stringify({
        cookieCount: afterCookies.length,
        sessionCookies: afterCookies.filter(c => !c.expires || c.expires === -1).length,
        authCookies: afterCookies.filter(c =>
          c.name.toLowerCase().includes('auth') ||
          c.name.toLowerCase().includes('token') ||
          c.name.toLowerCase().includes('session')
        ).map(c => ({ name: c.name, expires: c.expires ? new Date(c.expires * 1000).toISOString() : 'session' })),
        cookieChanges: {
          added: afterCookies.filter(ac => !beforeCookies.some(bc => bc.name === ac.name)).map(c => c.name),
          removed: beforeCookies.filter(bc => !afterCookies.some(ac => ac.name === bc.name)).map(c => c.name),
          changed: afterCookies.filter(ac =>
            beforeCookies.some(bc => bc.name === ac.name && bc.value !== ac.value)
          ).map(c => c.name)
        }
      }, null, 2));

      // Log page state details
      const pageState = await this.page.evaluate(() => {
        return {
          documentReadyState: document.readyState,
          url: window.location.href,
          hasUserElements: {
            profileIconExists: !!document.querySelector('.profile-icon, .avatar, .user-profile'),
            usernameDisplayExists: !!document.querySelector('.username, .user-name, .account-name'),
            loginButtonExists: !!document.querySelector('button[contains(text(), "Log in"), [data-test="login-button"]'),
          },
          documentTitle: document.title,
          // Look for common error messages
          visibleErrorText: Array.from(document.querySelectorAll('.error, .error-message, [role="alert"]'))
            .map(el => el.textContent.trim())
            .filter(text => text.length > 0)
        };
      });

      console.log('[LocationRefresh] Page state after navigation:', JSON.stringify(pageState, null, 2));

      // CHECKLIST ITEM 1.2: Post-Navigation Logged-In Indicator Check
      let loggedInIndicatorFound = false;
      try {
        // Try to wait for the logged-in indicator with a timeout
        const loggedInIndicator = await this.page.waitForSelector(config.selectors.login.successIndicator, {
          timeout: 5000,
          visible: true
        }).catch(() => null);

        loggedInIndicatorFound = !!loggedInIndicator;
        console.log(`[RefreshNavCheck] Logged-in indicator ${loggedInIndicatorFound ? 'found' : 'not found'} after keep-alive nav.`);
      } catch (indicatorError) {
        console.warn(`[RefreshNavCheck] Error checking for logged-in indicator: ${indicatorError.message}`);
        loggedInIndicatorFound = false;
      }

      // Take a screenshot of the current page state
      try {
        const timestamp = new Date().toISOString().replace(/:/g, '-');
        const screenshotPath = `location-refresh-${loggedInIndicatorFound ? 'success' : 'failure'}-${timestamp}.png`;
        await this.page.screenshot({ path: screenshotPath, fullPage: true });
        console.log(`[LocationRefresh] Saved current state screenshot to ${screenshotPath}`);
      } catch (screenshotError) {
        console.error('[LocationRefresh] Failed to take state screenshot:', screenshotError);
      }

      // CHECKLIST ITEM 1.3: Combined "Is Logged In" Determination
      const isStillLoggedInAfterNav = !isRedirectedToLogin && loggedInIndicatorFound;
      console.log(`[RefreshNavCheck] Determined login status after keep-alive nav: ${isStillLoggedInAfterNav}`);

      // Try alternative login check methods
      let alternativeLoginEvidence = false;
      if (!loggedInIndicatorFound) {
        const alternativeIndicators = await this.page.evaluate(() => {
          return {
            hasProfileMenu: !!document.querySelector('.profile, .account, .user-menu'),
            hasLogoutButton: !!document.querySelector('button:contains("Log out"), button:contains("Sign out")'),
            hasBalanceDisplay: !!document.querySelector('.balance, .wallet, .account-balance'),
            pageTitle: document.title,
            urlPath: window.location.pathname,
            hasLoginForm: !!document.querySelector('form[action*="login"], [data-test="login-form"]'),
          };
        });
        console.log('[LocationRefresh] Alternative login indicators:', JSON.stringify(alternativeIndicators, null, 2));

        // Check if we have alternative indicators suggesting logged-in state
        alternativeLoginEvidence = alternativeIndicators.hasProfileMenu ||
                                  alternativeIndicators.hasLogoutButton ||
                                  alternativeIndicators.hasBalanceDisplay;

        if (alternativeLoginEvidence) {
          console.log('[LocationRefresh] Alternative login indicators suggest we may still be logged in despite primary indicator failure');
        }
      }

      // CHECKLIST ITEM 3.1: Set isLoggedIn to false on detecting logout
      if (!isStillLoggedInAfterNav && !alternativeLoginEvidence) {
        console.warn('[RefreshState] Setting this.sessionData.isLoggedIn = false due to detected logout during refresh.');
        this.sessionData.isLoggedIn = false;
        this.sessionData.locationVerified = false;

        // CHECKLIST ITEM 2.1: Conditional Call to Full Login Method
        console.warn('[RefreshRecovery] Logged out during keep-alive. Attempting FULL RE-LOGIN via this.login()...');

        // CHECKLIST ITEM 2.3: Use await keyword for re-login call
        const reLoginSuccess = await this.login(true);

        // CHECKLIST ITEM 2.4: Outcome Handling of Re-Login Attempt
        if (reLoginSuccess) {
          console.log('[RefreshRecovery] Full re-login attempt SUCCEEDED during refresh cycle.');
          // Continue with location verification after successful re-login
          // Wait a moment for tokens to be properly set after login
          await new Promise(resolve => setTimeout(resolve, 1000));
        } else {
          console.error('[RefreshRecovery] Full re-login attempt FAILED during refresh cycle.');
          return; // Exit the refresh process if re-login failed
        }
      }

      // Wait a moment for any background location checks to complete
      console.log('[LocationRefresh] Pausing for 1000ms...');
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('[LocationRefresh] Pause complete.');

      // Update cookies and tokens
      this.sessionData.cookies = await this.page.cookies();
      this.sessionData.cookieHeader = this._formatCookieHeader(this.sessionData.cookies);

      // Check if we still have the location token
      if (!this.sessionData.encodedResponse) {
        console.warn('[LocationRefresh] Location token missing after refresh, waiting for it to be captured...');

        // Wait for tokens with timeout
        const startTime = Date.now();
        const tokenTimeout = 10000; // 10 seconds

        while (!this.sessionData.encodedResponse && Date.now() - startTime < tokenTimeout) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }

        if (!this.sessionData.encodedResponse) {
          console.error('[LocationRefresh] Failed to capture location token during refresh');
          this.sessionData.locationVerified = false;
          return;
        }
      }

      // Mark location as verified
      this.sessionData.locationVerified = true;
      console.log('[LocationRefresh] Location verification completed successfully');
    } catch (error) {
      console.error('[LocationRefresh] Error during location verification:', error);
      this.sessionData.locationVerified = false;

      // Potentially a navigation error - might be logged out
      console.warn('[RefreshState] Setting this.sessionData.isLoggedIn = false due to error during refresh.');
      this.sessionData.isLoggedIn = false;

      // Take a screenshot for debugging
      if (this.page) {
        try {
          const screenshotPath = `location-refresh-error-${Date.now()}.png`;
          await this.page.screenshot({ path: screenshotPath, fullPage: true });
          console.log(`[LocationRefresh] Saved error screenshot to ${screenshotPath}`);
        } catch (screenshotError) {
          console.error('[LocationRefresh] Failed to take error screenshot:', screenshotError);
        }
      }
    } finally {
      this.isRefreshingLocation = false;
    }
  }

  /**
   * Ensure the session is valid
   * @returns {Promise<boolean>} Whether the session is valid
   */
  async ensureSessionValid() {
    // If we're not initialized at all, we can't ensure a valid session
    if (!this.isInitialized) {
      console.error('Cannot ensure session validity: Underdog service not initialized');
      return false;
    }

    // If we're not logged in according to our state, we need to check if we can recover
    if (!this.sessionData.isLoggedIn) {
      console.log('Session marked as not logged in, checking if we can recover...');

      // Check if we have a browser and page
      if (!this.browser || !this.page) {
        console.error('Cannot recover session: browser or page is null');
        return false;
      }

      // Check if the page is closed
      try {
        if (this.page.isClosed && this.page.isClosed()) {
          console.error('Cannot recover session: page is closed');
          return false;
        }
      } catch (error) {
        console.error('Error checking if page is closed:', error);
        return false;
      }

      // Try to navigate to pick-em page to check if we're actually logged in
      try {
        await this.page.goto(config.endpoints.underdogPickem, {
          waitUntil: 'networkidle2',
          timeout: config.timeouts.navigation,
        });

        // Check if we're logged in
        const loggedIn = await this.page.$(config.selectors.login.successIndicator);

        if (loggedIn) {
          console.log('Found logged in state despite sessionData.isLoggedIn=false, recovering...');

          // Update our state
          this.sessionData.isLoggedIn = true;

          // Collect cookies
          this.sessionData.cookies = await this.page.cookies();
          this.sessionData.cookieHeader = this._formatCookieHeader(this.sessionData.cookies);

          // Check if we have tokens
          if (!this.sessionData.authorization || !this.sessionData.encodedResponse) {
            console.log('Missing tokens, waiting for them to be captured...');

            // Wait for tokens with timeout
            const startTime = Date.now();
            const tokenTimeout = 10000; // 10 seconds

            while (
              (!this.sessionData.authorization || !this.sessionData.encodedResponse) &&
              Date.now() - startTime < tokenTimeout
            ) {
              await new Promise(resolve => setTimeout(resolve, 500));
            }

            // Check if we have tokens now
            if (!this.sessionData.authorization || !this.sessionData.encodedResponse) {
              console.error('Failed to capture tokens during recovery');
              return false;
            }
          }

          return true;
        }

        // We're not logged in, and our state says we're not logged in
        // This is a terminal state - we don't attempt to log in here
        // The initial login should have happened during initialization
        console.error('Not logged in and cannot recover session');
        return false;
      } catch (error) {
        console.error('Error checking login state during recovery:', error);
        return false;
      }
    }

    // We're logged in according to our state, check if we have tokens
    if (!this.sessionData.authorization || !this.sessionData.encodedResponse) {
      console.error('Missing authentication tokens despite being logged in');
      return false;
    }

    // Check if location is verified
    if (!this.sessionData.locationVerified) {
      console.log('Location not verified, attempting to refresh location...');
      await this._refreshLocation();

      if (!this.sessionData.locationVerified) {
        console.error('Failed to verify location during session validation');
        return false;
      }
    }

    return true;
  }

  /**
   * Generate a share link for a bet
   * @param {Array<string>} betIds - Array of player IDs to bet on
   * @returns {Promise<Object>} Object with share_link and error properties
   */
  async generateShareLinkForBet(betIds) {
    const requestId = `req-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;

    try {
      console.log(`[${requestId}] Generating share link for bet:`, betIds);

      // First check if we're initialized at all
      if (!this.isInitialized) {
        console.error(`[${requestId}] Cannot generate share link: Underdog service not initialized`);
        return {
          share_link: null,
          error: 'Service Unavailable: Underdog service is not initialized'
        };
      }

      // Validate betIds
      if (!Array.isArray(betIds)) {
        console.error(`[${requestId}] Invalid input: betIds must be an array`);
        return {
          share_link: null,
          error: 'Invalid input: betIds must be an array'
        };
      }

      if (betIds.length === 0) {
        console.error(`[${requestId}] Invalid input: betIds array cannot be empty`);
        return {
          share_link: null,
          error: 'Invalid input: betIds array cannot be empty'
        };
      }

      // Check if the number of player IDs matches the expected count
      if (betIds.length !== config.bet.expectedIdsPerBet) {
        console.error(`[${requestId}] Invalid input: incorrect number of player IDs`, {
          expected: config.bet.expectedIdsPerBet,
          received: betIds.length
        });
        return {
          share_link: null,
          error: `Invalid input: expected ${config.bet.expectedIdsPerBet} player IDs, received ${betIds.length}`
        };
      }

      // Validate each player ID
      for (let i = 0; i < betIds.length; i++) {
        const playerId = betIds[i];

        if (typeof playerId !== 'string') {
          console.error(`[${requestId}] Invalid input: player ID must be a string`, {
            index: i,
            type: typeof playerId
          });
          return {
            share_link: null,
            error: `Invalid input: player ID at index ${i} must be a string`
          };
        }

        if (playerId.trim() === '') {
          console.error(`[${requestId}] Invalid input: player ID cannot be empty`, {
            index: i
          });
          return {
            share_link: null,
            error: `Invalid input: player ID at index ${i} cannot be empty`
          };
        }
      }

      // Ensure we have a valid session - CRITICAL: This should not attempt to log in
      // if we're not already logged in, as that should have happened during initialization
      console.log(`[${requestId}] Ensuring session is valid...`);
      const sessionValid = await this.ensureSessionValid();

      if (!sessionValid) {
        console.error(`[${requestId}] Session validation failed`);
        return {
          share_link: null,
          error: 'Service Unavailable: Session validation failed. The service may need to be restarted.'
        };
      }

      // Place the bet
      console.log(`[${requestId}] Placing bet...`);
      const betId = await this._placeBet(betIds);

      if (!betId) {
        console.error(`[${requestId}] Failed to place bet`);

        // Check if we have auth tokens
        if (!this.sessionData.authorization || !this.sessionData.encodedResponse) {
          console.error(`[${requestId}] Missing authentication tokens during bet placement`);
          return {
            share_link: null,
            error: 'Authentication error: Missing required tokens. The service may need to be restarted.'
          };
        }

        return {
          share_link: null,
          error: 'Failed to place bet with the provided player IDs. Check server logs for detailed error information.'
        };
      }

      // Generate the share link
      console.log(`[${requestId}] Generating share link for bet ID: ${betId}`);
      const shareLink = await this._generateShareLink(betId);

      if (!shareLink) {
        console.error(`[${requestId}] Failed to generate share link for bet ID: ${betId}`);
        return {
          share_link: null,
          error: 'Failed to generate share link for the placed bet'
        };
      }

      console.log(`[${requestId}] Successfully generated share link: ${shareLink}`);
      return {
        share_link: shareLink,
        error: null
      };
    } catch (error) {
      console.error(`[${requestId}] Unexpected error generating share link:`, error);

      // Check for specific error types
      if (error.message && error.message.includes('net::ERR_')) {
        return {
          share_link: null,
          error: 'Network error: Unable to communicate with Underdog Fantasy'
        };
      }

      if (error.message && error.message.includes('Protocol error')) {
        return {
          share_link: null,
          error: 'Browser error: The browser session may have been terminated'
        };
      }

      // Check for location error
      if (error.isAxiosError && error.response?.status === 422) {
        const responseData = error.response.data;
        if (responseData && responseData.error &&
            responseData.error.api_code === 'user_dismiss' &&
            responseData.error.title === 'Location Error') {

          console.warn(`[${requestId}] Encountered Underdog Location Error (Code: TE) during bet placement. Session location likely stale.`);

          // Mark location as needing verification
          this.sessionData.locationVerified = false;

          // Trigger an immediate location refresh
          console.log(`[${requestId}] Triggering immediate location refresh due to location error.`);
          this._refreshLocation().catch(e => console.error(`[${requestId}] Error from immediate location refresh:`, e));

          return {
            share_link: null,
            error: `Failed to place bet: Underdog reported a location verification error. Please try again shortly as the session is being refreshed. (Code: TE) - ${responseData.error.detail || 'Location verification required'}`
          };
        }
      }

      return {
        share_link: null,
        error: `Unexpected error: ${error.message}`
      };
    }
  }

  /**
   * Place a bet with the given player IDs
   */
  async _placeBet(playerIds) {
    const requestId = `req-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;

    try {
      console.log(`[${requestId}] [BetService] Placing bet with player IDs:`, playerIds);

      // Validate auth tokens before making the request
      console.log(`[${requestId}] [BetService] Validating auth tokens...`);
      if (!this.sessionData.authorization) {
        console.error(`[${requestId}] [BetService] Missing authorization token`);
        return null;
      }

      if (!this.sessionData.encodedResponse) {
        console.error(`[${requestId}] [BetService] Missing User-Location-Token`);
        return null;
      }

      if (!this.sessionData.deviceId) {
        console.warn(`[${requestId}] [BetService] Missing Client-Device-Id, will use fallback`);
      }

      // Prepare request URL
      const url = config.endpoints.underdogBetSlip;
      console.log(`[${requestId}] [BetService] API URL: ${url}`);

      // Prepare request headers
      const headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0',
        'Accept': 'application/json',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Content-Type': 'application/json',
        'Client-Type': 'web',
        'Client-Version': '20250513165939',
        'User-Location-Token': this.sessionData.encodedResponse,
        'Client-Device-Id': this.sessionData.deviceId || '047ed1c1-77f1-443e-bb36-78b17b257bd5',
        'Client-Request-Id': `${Date.now()}-${Math.random().toString(36).substring(2, 15)}`,
        'Authorization': this.sessionData.authorization,
        'Origin': 'https://underdogfantasy.com',
        'Connection': 'keep-alive',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'Priority': 'u=0',
        'TE': 'trailers',
        'Cookie': this.sessionData.cookieHeader,
      };

      console.log(`[${requestId}] [BetService] Headers for "place bet" API request:`, {
        'Authorization': headers.Authorization ? `${headers.Authorization.substring(0, 15)}...` : 'MISSING',
        'User-Location-Token': headers['User-Location-Token'] ? `${headers['User-Location-Token'].substring(0, 15)}...` : 'MISSING',
        'Client-Device-Id': headers['Client-Device-Id'],
        'Client-Request-Id': headers['Client-Request-Id'],
        'Cookie': headers.Cookie ? `${headers.Cookie.substring(0, 20)}...` : 'MISSING'
      });

      // Prepare request body
      const body = {
        entry_slip: {
          fee: "1",
          options: playerIds.map(id => ({
            id: id,
            power_up_id: null,
            type: "OverUnderOption",
          })),
          total_multiplier: "6.0",
          power_up_id: null,
          insured: false,
        },
      };
      console.log(`[${requestId}] [BetService] Request payload:`, JSON.stringify(body));

      // Make the API request
      console.log(`[${requestId}] [BetService] Attempting to POST to place bet API...`);
      const response = await axios.post(url, body, {
        headers,
        timeout: 30000, // 30 second timeout
        validateStatus: null // Don't throw on any status code
      });

      console.log(`[${requestId}] [BetService] API response status:`, response.status);

      // Check response
      if (response.status !== 200 && response.status !== 201) {
        console.error(`[${requestId}] [BetService] API returned non-success status code:`, {
          status: response.status,
          statusText: response.statusText,
          data: response.data
        });
        return null;
      }

      if (!response.data || !response.data.entry_slip.id) {
        console.error(`[${requestId}] [BetService] Invalid response from bet slip API:`, response.data);
        return null;
      }

      console.log(`[${requestId}] [BetService] Bet placed successfully, ID:`, response.data.id);
      return response.data.entry_slip.id;
    } catch (error) {
      console.error(`[${requestId}] [BetService] Failed to place bet:`, error);

      // Detailed error logging
      if (error.isAxiosError) {
        console.error(`[${requestId}] [BetService] Axios error details:`, {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          message: error.message,
          code: error.code
        });

        // Check for specific error types
        if (error.code === 'ECONNABORTED') {
          console.error(`[${requestId}] [BetService] Request timed out`);
        } else if (error.response?.status === 401) {
          console.error(`[${requestId}] [BetService] Authentication failed (401)`);
        } else if (error.response?.status === 403) {
          console.error(`[${requestId}] [BetService] Authorization failed (403)`);
        } else if (error.response?.status === 422) {
          const responseData = error.response.data;
          if (responseData && responseData.error &&
              responseData.error.api_code === 'user_dismiss' &&
              responseData.error.title === 'Location Error') {
            console.warn(`[${requestId}] [BetService] Encountered Underdog Location Error (Code: TE) during bet placement. Session location likely stale.`);
            console.warn(`[${requestId}] [BetService] Error details: ${JSON.stringify(responseData.error)}`);

            // Mark location as needing verification
            this.sessionData.locationVerified = false;

            // Trigger an immediate location refresh
            console.log(`[${requestId}] [BetService] Triggering immediate location refresh due to location error.`);
            this._refreshLocation().catch(e => console.error(`[${requestId}] [BetService] Error from immediate location refresh:`, e));
          } else {
            console.error(`[${requestId}] [BetService] Unprocessable Entity error (422)`, error.response?.data);
          }
        } else if (error.response?.status === 500) {
          console.error(`[${requestId}] [BetService] Server error (500)`, error.response?.data);
        }
      }

      return null;
    }
  }

  /**
   * Generate a share link for a bet
   */
  async _generateShareLink(betId) {
    const requestId = `req-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;

    try {
      console.log(`[${requestId}] [ShareService] Generating share link for bet ID:`, betId);

      // Validate auth tokens before making the request
      console.log(`[${requestId}] [ShareService] Validating auth tokens...`);
      if (!this.sessionData.authorization) {
        console.error(`[${requestId}] [ShareService] Missing authorization token`);
        return null;
      }

      if (!this.sessionData.encodedResponse) {
        console.error(`[${requestId}] [ShareService] Missing User-Location-Token`);
        return null;
      }

      // Prepare request URL
      const url = config.endpoints.underdogShareLink.replace('{id}', betId);
      console.log(`[${requestId}] [ShareService] API URL: ${url}`);

      // Prepare request headers
      const headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0',
        'Accept': 'application/json',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Content-Type': 'application/json',
        'Client-Type': 'web',
        'Client-Version': '20250513165939',
        'User-Location-Token': this.sessionData.encodedResponse,
        'Client-Device-Id': this.sessionData.deviceId || '047ed1c1-77f1-443e-bb36-78b17b257bd5',
        'Client-Request-Id': `${Date.now()}-${Math.random().toString(36).substring(2, 15)}`,
        'Authorization': this.sessionData.authorization,
        'Origin': 'https://underdogfantasy.com',
        'Connection': 'keep-alive',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'Priority': 'u=0',
        'Content-Length': '0',
        'TE': 'trailers',
        'Cookie': this.sessionData.cookieHeader,
      };

      console.log(`[${requestId}] [ShareService] Headers for "share link" API request:`, {
        'Authorization': headers.Authorization ? `${headers.Authorization.substring(0, 15)}...` : 'MISSING',
        'User-Location-Token': headers['User-Location-Token'] ? `${headers['User-Location-Token'].substring(0, 15)}...` : 'MISSING',
        'Client-Device-Id': headers['Client-Device-Id'],
        'Client-Request-Id': headers['Client-Request-Id']
      });

      // Make the API request
      console.log(`[${requestId}] [ShareService] Attempting to POST to share link API...`);
      const response = await axios.post(url, {}, {
        headers,
        timeout: 30000, // 30 second timeout
        validateStatus: null // Don't throw on any status code
      });

      console.log(`[${requestId}] [ShareService] API response status:`, response.status);

      // Check response
      if (response.status !== 200 && response.status !== 201) {
        console.error(`[${requestId}] [ShareService] API returned non-success status code:`, {
          status: response.status,
          statusText: response.statusText,
          data: response.data
        });
        return null;
      }

      if (!response.data || !response.data.share_link) {
        console.error(`[${requestId}] [ShareService] Invalid response from share link API:`, response.data);
        return null;
      }

      console.log(`[${requestId}] [ShareService] Share link generated successfully:`, response.data.url);
      return response.data.share_link.url;
    } catch (error) {
      console.error(`[${requestId}] [ShareService] Failed to generate share link:`, error);

      // Detailed error logging
      if (error.isAxiosError) {
        console.error(`[${requestId}] [ShareService] Axios error details:`, {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          message: error.message,
          code: error.code
        });
      }

      return null;
    }
  }

  /**
   * Shutdown the service
   */
  async shutdown() {
    try {
      console.log('Shutting down Underdog service...');

      // Clear refresh timers
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer);
        this.refreshTimer = null;
        console.log('Session refresh timer cleared');
      }

      if (this.locationRefreshTimer) {
        clearInterval(this.locationRefreshTimer);
        this.locationRefreshTimer = null;
        console.log('Location refresh timer cleared');
      }

      // Reset session data
      this.sessionData = {
        isLoggedIn: false,
        authorization: null,
        encodedResponse: null,
        deviceId: null,
        cookies: null,
        cookieHeader: null,
        locationVerified: false,
      };

      // Close browser
      if (this.browser) {
        try {
          console.log('Closing browser...');
          await this.browser.close();
          console.log('Browser closed successfully');
        } catch (browserError) {
          console.error('Error closing browser:', browserError);
        }

        this.browser = null;
        this.page = null;
      } else {
        console.log('Browser was already closed');
      }

      // Reset state flags
      this.isInitialized = false;
      this.isLoggingIn = false;
      this.isRefreshingToken = false;
      this.initializationAttempted = true;
      this.initializationSuccessful = false;

      console.log('Underdog service shut down successfully');
    } catch (error) {
      console.error('Error shutting down Underdog service:', error);

      // Force reset of critical state even if shutdown fails
      this.browser = null;
      this.page = null;
      this.isInitialized = false;
      this.refreshTimer = null;
      this.locationRefreshTimer = null;
    }
  }
}

module.exports = new UnderdogService();
