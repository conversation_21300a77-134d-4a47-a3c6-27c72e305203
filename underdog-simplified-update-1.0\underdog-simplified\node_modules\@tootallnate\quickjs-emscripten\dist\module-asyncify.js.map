{"version": 3, "file": "module-asyncify.js", "sourceRoot": "", "sources": ["../ts/module-asyncify.ts"], "names": [], "mappings": ";;;AAEA,qCAAgD;AAEhD,yCAA4C;AAC5C,qCAKiB;AACjB,yDAAwD;AAGxD;;;;;;;;;;;;GAYG;AACH,MAAa,sBAAuB,SAAQ,0BAAiB;IAM3D,eAAe;IACf,YAAY,MAAoC,EAAE,GAAoB;QACpE,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;QAClB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;QACd,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;IACtB,CAAC;IAED;;;;OAIG;IACM,UAAU,CAAC,UAA+B,EAAE;QACnD,MAAM,EAAE,GAAG,IAAI,mBAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,EAAE,SAAS,EAAE,CAAC,MAAM,EAAE,EAAE;YACvE,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;YACpC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,CAAC,CAAA;QAClC,CAAC,CAAC,CAAA;QACF,MAAM,OAAO,GAAG,IAAI,sCAAmB,CAAC;YACtC,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,EAAE;YACF,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC,CAAA;QAEF,IAAA,gCAAuB,EAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QAEzC,IAAI,OAAO,CAAC,YAAY,EAAE;YACxB,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,YAAY,CAAC,CAAA;SAC9C;QAED,OAAO,OAAO,CAAA;IAChB,CAAC;IAED;;;;OAIG;IACM,UAAU,CAAC,UAA0B,EAAE;QAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAA;QACjC,MAAM,SAAS,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAA;QAC/F,MAAM,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,EAAE,GAAG,OAAO,EAAE,cAAc,EAAE,SAAS,EAAE,CAAC,CAAA;QAC7E,OAAO,CAAC,OAAO,GAAG,OAAO,CAAA;QACzB,OAAO,OAAO,CAAA;IAChB,CAAC;IAED,6CAA6C;IACpC,QAAQ;QACf,MAAM,IAAI,8BAAqB,CAAC,+DAA+D,CAAC,CAAA;IAClG,CAAC;IAED;;;;;;;;;;OAUG;IACH,aAAa,CAAC,IAAY,EAAE,OAA0B;QACpD,uEAAuE;QACvE,OAAO,gBAAK,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YAC1C,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAA;YAC1C,IAAA,sCAA6B,EAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;YAClD,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC,aAAa,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;YAEtD,IAAI,OAAO,CAAC,gBAAgB,KAAK,SAAS,EAAE;gBAC1C,sEAAsE;gBACtE,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAA;aAC9B;YAED,IAAI,MAAM,CAAC,KAAK,EAAE;gBAChB,MAAM,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;gBACjD,MAAM,KAAK,CAAA;aACZ;YAED,MAAM,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;YACjD,OAAO,KAAK,CAAA;QACd,CAAC,CAAC,CAAA;IACJ,CAAC;CACF;AAzFD,wDAyFC", "sourcesContent": ["import { <PERSON>JSAsyncContext } from \"./context-asyncify\"\nimport { QuickJSAsyncEmscriptenModule } from \"./emscripten-types\"\nimport { QuickJSNotImplemented } from \"./errors\"\nimport { <PERSON>JSAsyncFFI } from \"./variants\"\nimport { Lifetime, Scope } from \"./lifetime\"\nimport {\n  applyBaseRuntimeOptions,\n  applyModuleEvalRuntimeOptions,\n  ModuleEvalOptions,\n  QuickJSWASMModule,\n} from \"./module\"\nimport { QuickJSAsyncRuntime } from \"./runtime-asyncify\"\nimport { AsyncRuntimeOptions, ContextOptions, RuntimeOptions } from \"./types\"\n\n/**\n * Asyncified version of [[QuickJSWASMModule]].\n *\n * Due to limitations of Emscripten's ASYNCIFY process, only a single async\n * function call can happen at a time across the entire WebAssembly module.\n *\n * That means that all runtimes, contexts, functions, etc created inside this\n * WebAssembly are limited to a single concurrent async action.\n * **Multiple concurrent async actions is an error.**\n *\n * To allow for multiple concurrent async actions, you must create multiple WebAssembly\n * modules.\n */\nexport class QuickJSAsyncWASMModule extends QuickJSWASMModule {\n  /** @private */\n  protected ffi: QuickJSAsyncFFI\n  /** @private */\n  protected module: QuickJSAsyncEmscriptenModule\n\n  /** @private */\n  constructor(module: QuickJSAsyncEmscriptenModule, ffi: QuickJSAsyncFFI) {\n    super(module, ffi)\n    this.ffi = ffi\n    this.module = module\n  }\n\n  /**\n   * Create a new async runtime inside this WebAssembly module. All runtimes inside a\n   * module are limited to a single async call at a time. For multiple\n   * concurrent async actions, create multiple WebAssembly modules.\n   */\n  override newRuntime(options: AsyncRuntimeOptions = {}): QuickJSAsyncRuntime {\n    const rt = new Lifetime(this.ffi.QTS_NewRuntime(), undefined, (rt_ptr) => {\n      this.callbacks.deleteRuntime(rt_ptr)\n      this.ffi.QTS_FreeRuntime(rt_ptr)\n    })\n    const runtime = new QuickJSAsyncRuntime({\n      module: this.module,\n      ffi: this.ffi,\n      rt,\n      callbacks: this.callbacks,\n    })\n\n    applyBaseRuntimeOptions(runtime, options)\n\n    if (options.moduleLoader) {\n      runtime.setModuleLoader(options.moduleLoader)\n    }\n\n    return runtime\n  }\n\n  /**\n   * A simplified API to create a new [[QuickJSRuntime]] and a\n   * [[QuickJSContext]] inside that runtime at the same time. The runtime will\n   * be disposed when the context is disposed.\n   */\n  override newContext(options: ContextOptions = {}): QuickJSAsyncContext {\n    const runtime = this.newRuntime()\n    const lifetimes = options.ownedLifetimes ? options.ownedLifetimes.concat([runtime]) : [runtime]\n    const context = runtime.newContext({ ...options, ownedLifetimes: lifetimes })\n    runtime.context = context\n    return context\n  }\n\n  /** Synchronous evalCode is not supported. */\n  override evalCode(): never {\n    throw new QuickJSNotImplemented(\"QuickJSWASMModuleAsyncify.evalCode: use evalCodeAsync instead\")\n  }\n\n  /**\n   * One-off evaluate code without needing to create a [[QuickJSRuntimeAsync]] or\n   * [[QuickJSContextSync]] explicitly.\n   *\n   * This version allows for asynchronous Ecmascript module loading.\n   *\n   * Note that only a single async action can occur at a time inside the entire WebAssembly module.\n   * **Multiple concurrent async actions is an error.**\n   *\n   * See the documentation for [[QuickJSWASMModule.evalCode]] for more details.\n   */\n  evalCodeAsync(code: string, options: ModuleEvalOptions): Promise<unknown> {\n    // TODO: we should really figure out generator for the Promise monad...\n    return Scope.withScopeAsync(async (scope) => {\n      const vm = scope.manage(this.newContext())\n      applyModuleEvalRuntimeOptions(vm.runtime, options)\n      const result = await vm.evalCodeAsync(code, \"eval.js\")\n\n      if (options.memoryLimitBytes !== undefined) {\n        // Remove memory limit so we can dump the result without exceeding it.\n        vm.runtime.setMemoryLimit(-1)\n      }\n\n      if (result.error) {\n        const error = vm.dump(scope.manage(result.error))\n        throw error\n      }\n\n      const value = vm.dump(scope.manage(result.value))\n      return value\n    })\n  }\n}\n"]}