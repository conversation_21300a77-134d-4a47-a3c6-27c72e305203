# Enhanced Underdog Fantasy Automation v1.3

A production-ready Node.js application for automating Underdog Fantasy bet placement and share link generation with advanced proxy support and anti-detection measures.

## Overview

This application provides a simple HTTP API for generating share links for Underdog Fantasy bets. It maintains a long-lived Puppeteer session to avoid repeated logins and provides a single endpoint for generating share links.

## Key Features

### 🚀 Core Features
- **One-time Initialization**: The application initializes and logs in to Underdog Fantasy exactly once during startup
- **Geolocation Handling**: Automatically handles geolocation permission prompts with US-based location spoofing
- **Session Management**: Maintains a single browser session with periodic refresh
- **Location Verification**: Proactively maintains location verification status to prevent location errors
- **Robust Error Handling**: Gracefully handles browser initialization failures and login issues
- **Simple API**: Single endpoint for generating share links with consistent response format

### 🌐 Proxy Support (NEW)
- **Multiple Proxy Types**: Support for HTTP, HTTPS, SOCKS4, and SOCKS5 proxies
- **Proxy Authentication**: Full support for username/password authentication
- **Automatic Configuration**: Easy setup via environment variables
- **Connection Testing**: Built-in proxy connectivity validation

### 🛡️ Anti-Detection & Stealth (NEW)
- **Advanced Stealth Measures**: Uses puppeteer-extra-plugin-stealth to avoid detection
- **Navigator.webdriver Removal**: Eliminates automation indicators
- **Realistic Browser Fingerprinting**: Mimics real user behavior patterns
- **Human-like Interactions**: Random delays and typing patterns
- **Developer Mode Detection**: Automatically detects and handles "user_dismiss" errors

### 🔄 Enhanced Reliability (NEW)
- **Login Retry Logic**: Configurable retry attempts with exponential backoff
- **Error Recovery**: Automatic recovery from common failure scenarios
- **Session Persistence**: Improved token capture and session management
- **Health Monitoring**: Enhanced browser health checks and recovery

## Requirements

- Node.js 18 or higher
- Underdog Fantasy account credentials

## Installation

1. Clone this repository
2. Install dependencies:
   ```
   npm install
   ```
3. Create a `.env` file based on `.env.example`:
   ```
   cp .env.example .env
   ```
4. Edit the `.env` file to add your Underdog Fantasy credentials

## Configuration

The application is configured using environment variables:

### 🔐 Required Credentials
- `UNDERDOG_USERNAME`: Your Underdog Fantasy email
- `UNDERDOG_PASSWORD`: Your Underdog Fantasy password

### 🌐 Proxy Configuration (for restricted regions)
- `PROXY_SERVER`: Proxy server address (format: `host:port`)
- `PROXY_USERNAME`: Proxy authentication username (optional)
- `PROXY_PASSWORD`: Proxy authentication password (optional)
- `PROXY_TYPE`: Proxy type - `http`, `https`, `socks4`, or `socks5` (default: `http`)

### 🖥️ Server & Browser Settings
- `PORT`: HTTP server port (default: 3000)
- `HEADLESS`: Whether to run the browser in headless mode (default: true)
- `USER_DATA_DIR`: Directory to store browser user data (default: ./user_data)

### ⏱️ Timeout Configuration
- `LOGIN_TIMEOUT_MS`: Timeout for login operations in milliseconds (default: 60000)
- `NAVIGATION_TIMEOUT_MS`: Timeout for navigation operations in milliseconds (default: 45000)
- `SESSION_REFRESH_INTERVAL_MINUTES`: Interval to refresh the session in minutes (default: 120)
- `LOCATION_REFRESH_INTERVAL_MINUTES`: Interval to refresh location verification in minutes (default: 4)

### 🛡️ Stealth & Anti-Detection Settings
- `STEALTH_ENABLED`: Enable stealth plugin (default: true)
- `REMOVE_WEBDRIVER`: Remove navigator.webdriver property (default: true)
- `FAKE_USER_AGENT`: Use fake user agent (default: true)
- `FAKE_VIEWPORT`: Use fake viewport (default: true)
- `FAKE_LANGUAGES`: Use fake languages (default: true)
- `FAKE_TIMEZONE`: Use fake timezone (default: false - keeps real timezone for location)

### 🔄 Retry & Recovery Settings
- `MAX_LOGIN_ATTEMPTS`: Maximum login retry attempts (default: 3)
- `LOGIN_RETRY_DELAY_MS`: Delay between login retries in milliseconds (default: 5000)
- `MAX_LOCATION_REFRESH_ATTEMPTS`: Maximum location refresh attempts (default: 2)

### 🎯 Bet Configuration
- `EXPECTED_IDS_PER_BET`: Expected number of player IDs in a bet (default: 3)

## 🌐 Proxy Setup Guide

### Why Use a Proxy?
If you're testing from a restricted region (outside the US), you'll need a US-based proxy to access Underdog Fantasy. The application includes full proxy support with authentication.

### Supported Proxy Types
- **HTTP/HTTPS**: Standard web proxies
- **SOCKS4/SOCKS5**: More advanced proxy protocols with better performance

### Proxy Configuration Examples

#### HTTP Proxy without Authentication
```bash
PROXY_SERVER=proxy.example.com:8080
PROXY_TYPE=http
```

#### HTTP Proxy with Authentication
```bash
PROXY_SERVER=proxy.example.com:8080
PROXY_USERNAME=your_username
PROXY_PASSWORD=your_password
PROXY_TYPE=http
```

#### SOCKS5 Proxy with Authentication
```bash
PROXY_SERVER=socks.example.com:1080
PROXY_USERNAME=your_username
PROXY_PASSWORD=your_password
PROXY_TYPE=socks5
```

### Recommended Proxy Providers
For testing purposes, consider these reliable proxy providers:
- **Bright Data** (formerly Luminati) - Premium residential proxies
- **Oxylabs** - High-quality datacenter and residential proxies
- **ProxyMesh** - Affordable rotating proxies
- **Storm Proxies** - Budget-friendly options

### Proxy Testing
The application will automatically test proxy connectivity during startup. If the proxy fails, you'll see detailed error messages in the logs.

## Usage

1. Start the server:
   ```
   npm start
   ```

2. The server will start on the configured port and initialize the Underdog service in the background.
   - The HTTP server starts immediately and can accept requests
   - The Underdog service initialization (browser launch and login) happens in the background
   - Requests to the API will wait for initialization to complete if it's still in progress
   - If initialization fails, the API will return appropriate error responses

3. Use the API to generate share links:
   ```
   POST /generate-links
   Content-Type: application/json

   {
     "betIds": ["player_id_1", "player_id_2", "player_id_3"]
   }
   ```

4. The API will respond with:
   ```
   {
     "share_link": "https://underdogfantasy.com/share/abc123",
     "error": null
   }
   ```

   Or in case of an error:
   ```
   {
     "share_link": null,
     "error": "Error message"
   }
   ```

## API Endpoints

### `GET /health`

Returns the health status of the application.

**Response:**
```json
{
  "status": "ok",
  "initialized": true,
  "uptime": 123.45,
  "timestamp": "2023-06-01T12:34:56.789Z"
}
```

If the service failed to initialize:
```json
{
  "status": "error",
  "initialized": false,
  "error": "Error message",
  "uptime": 123.45,
  "timestamp": "2023-06-01T12:34:56.789Z"
}
```

### `POST /generate-links`

Generates a share link for a bet.

**Request Body:**
```json
{
  "betIds": ["player_id_1", "player_id_2", "player_id_3"]
}
```

**Success Response:**
```json
{
  "share_link": "https://underdogfantasy.com/share/abc123",
  "error": null
}
```

**Error Response:**
```json
{
  "share_link": null,
  "error": "Error message"
}
```

## Architecture

The application consists of three main components:

1. **HTTP Server (server.js)**: Handles HTTP requests and responses
2. **Underdog Service (underdogService.js)**: Manages the Puppeteer browser, login, and bet placement
3. **Configuration (config.js)**: Loads and validates configuration from environment variables

## Session Management

The application maintains a single Puppeteer browser session to avoid repeated logins. The session is initialized once during application startup and is refreshed periodically to prevent expiration.

Key aspects of session management:
- **One-time Initialization**: The browser is launched and login is performed exactly once during startup
- **Geolocation Handling**: The application automatically grants geolocation permissions to prevent prompts
- **Session Refresh**: The session is refreshed periodically based on the configured interval
- **Location Verification**: Location status is proactively refreshed more frequently than the general session to prevent "Location Error (Code: TE)" issues
- **Token Management**: Authentication tokens are captured and reused for API requests

## Error Handling

The application includes robust error handling for common scenarios:

- **Input Validation**: Validates request parameters and returns appropriate error messages
- **Initialization Failures**: Gracefully handles browser initialization failures
- **Login Failures**: Detects and reports login failures with detailed error messages
- **Session Validation**: Ensures the session is valid before making API requests
- **Location Errors**: Detects and automatically recovers from location verification errors
- **API Failures**: Handles API request failures with appropriate error responses

## Debugging

The application includes several debugging features:

- **Request IDs**: Each request is assigned a unique ID for tracking through logs
- **Detailed Logging**: Comprehensive logging of initialization, login, and API requests
- **Screenshots**: Takes screenshots of the browser during initialization and login failures (when headless=false)
- **HTML Dumps**: Saves HTML content during failures for debugging (when headless=false)

## Limitations

- The application is designed to run in a single process
- No advanced logging or monitoring beyond console output
- No authentication for the API endpoints
- No rate limiting

## 🔧 Troubleshooting

### Common Issues and Solutions

#### "Developer Mode Detected" Error
**Error**: `user_dismiss: Disable developer mode in your browser and try again`

**Solutions**:
1. Ensure `STEALTH_ENABLED=true` in your `.env` file
2. Run the application in headless mode (`HEADLESS=true`)
3. Clear the user data directory and restart
4. Check that all stealth plugins are properly configured

#### Proxy Connection Issues
**Error**: Network timeouts or connection refused errors

**Solutions**:
1. Verify proxy server address and port are correct
2. Test proxy connectivity manually using curl or browser
3. Check proxy authentication credentials
4. Try different proxy types (HTTP vs SOCKS5)
5. Ensure proxy IP is from a supported US region

#### Login Failures
**Error**: Login attempts fail repeatedly

**Solutions**:
1. Verify Underdog Fantasy credentials are correct
2. Check if account is locked or requires verification
3. Increase `MAX_LOGIN_ATTEMPTS` and `LOGIN_RETRY_DELAY_MS`
4. Run in non-headless mode to see what's happening
5. Check for CAPTCHA or additional verification steps

#### Location Verification Errors
**Error**: `Location Error (Code: TE)` with 422 status

**Solutions**:
1. Ensure geolocation is set to a US location
2. Check `LOCATION_REFRESH_INTERVAL_MINUTES` setting
3. Verify proxy IP is from a supported region
4. Clear browser data and restart the service

### Debug Mode
To enable debug mode for troubleshooting:
```bash
HEADLESS=false
```
This will show the browser window so you can see what's happening during automation.

### Log Analysis
The application provides detailed logging. Look for these key indicators:
- `[BrowserHealth]` - Browser status and health checks
- `[LocationRefresh]` - Location verification attempts
- `[NetworkMonitor]` - API request/response monitoring
- `[RefreshState]` - Session state changes

## Recent Updates

### Version 1.3 - Enhanced Proxy & Stealth Support

#### New Features
- **Advanced Proxy Support**: Full HTTP/HTTPS/SOCKS4/SOCKS5 proxy support with authentication
- **Enhanced Stealth Measures**: Uses puppeteer-extra-plugin-stealth to avoid detection
- **Login Retry Logic**: Configurable retry attempts with intelligent error handling
- **Developer Mode Detection**: Automatic detection and handling of "user_dismiss" errors
- **Human-like Behavior**: Random delays and typing patterns to mimic real users
- **Improved Error Recovery**: Better handling of common failure scenarios

#### Bug Fixes
- Fixed intermittent login failures during LocationRefresh checks
- Improved session token capture reliability
- Enhanced browser health monitoring and recovery
- Better handling of proxy authentication

### Version 1.0 - Location Verification Enhancement

The application includes proactive location verification to prevent the "Location Error (Code: TE)" with 422 status that occurs when placing bets after a period of server inactivity:

- Added a separate location verification refresh cycle that runs more frequently than the general session refresh
- Implemented specific error handling for location verification errors
- Added automatic recovery from location verification failures
- Configurable location refresh interval via `LOCATION_REFRESH_INTERVAL_MINUTES` environment variable
