# 🚀 Deployment Guide - Enhanced Underdog Fantasy Automation v1.3

## 📋 Pre-Deployment Checklist

### System Requirements
- ✅ Node.js 18+ installed
- ✅ npm or yarn package manager
- ✅ Valid Underdog Fantasy account
- ✅ US-based proxy (if deploying from restricted region)
- ✅ Sufficient system resources (2GB+ RAM recommended)

### Account Requirements
- ✅ Active Underdog Fantasy account
- ✅ Account verified and in good standing
- ✅ No pending verification requirements
- ✅ Account accessible from target region

## 🔧 Installation Steps

### 1. Download and Extract
```bash
# Extract the application files
cd /path/to/your/deployment/directory
# Files should be extracted to: underdog-simplified-update-1.0/underdog-simplified/
```

### 2. Install Dependencies
```bash
cd underdog-simplified-update-1.0/underdog-simplified
npm install
```

### 3. Configure Environment
```bash
# Copy the example environment file
cp .env.example .env

# Edit the .env file with your settings
nano .env  # or use your preferred editor
```

### 4. Required Configuration
Edit `.env` file with your credentials:

```bash
# Required: Underdog Fantasy Credentials
UNDERDOG_USERNAME=<EMAIL>
UNDERDOG_PASSWORD=your_secure_password

# Required for restricted regions: Proxy Configuration
PROXY_SERVER=your-proxy-host:port
PROXY_USERNAME=your_proxy_username
PROXY_PASSWORD=your_proxy_password
PROXY_TYPE=http

# Recommended: Security Settings
STEALTH_ENABLED=true
HEADLESS=true
```

## 🧪 Testing Before Deployment

### 1. Basic Connectivity Test
```bash
# Test without proxy (if in supported region)
npm start

# Check health endpoint
curl http://localhost:3000/health
```

### 2. Proxy Connectivity Test (if using proxy)
```bash
# Test proxy connection manually
curl --proxy ***********************************:port https://ipinfo.io

# Should return US-based IP information
```

### 3. Application Test
```bash
# Start the application
npm start

# In another terminal, test the API
npm test

# Or manually test with curl
curl -X POST http://localhost:3000/generate-links \
  -H "Content-Type: application/json" \
  -d '{"betIds":["55875246-0e29-4387-a20e-9a0ef9a968ba","e0847a4a-4aa6-4b3a-b42f-6bbe7a81f86f","a763577d-d951-45bf-897d-e8264f0db863"]}'
```

## 🌐 Production Deployment Options

### Option 1: Local Server Deployment
```bash
# Start the application
npm start

# The server will run on http://localhost:3000
# Use a process manager like PM2 for production:
npm install -g pm2
pm2 start server.js --name underdog-automation
```

### Option 2: Docker Deployment (Advanced)
Create a `Dockerfile`:
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

Build and run:
```bash
docker build -t underdog-automation .
docker run -d -p 3000:3000 --env-file .env underdog-automation
```

### Option 3: Cloud Deployment
For cloud deployment (AWS, GCP, Azure):
1. Ensure proxy configuration is properly set
2. Use environment variables for sensitive data
3. Configure health checks on `/health` endpoint
4. Set up proper logging and monitoring

## 🔍 Monitoring and Maintenance

### Health Monitoring
```bash
# Check application health
curl http://localhost:3000/health

# Expected response:
{
  "status": "ok",
  "initialized": true,
  "uptime": 123.45,
  "timestamp": "2023-06-01T12:34:56.789Z"
}
```

### Log Monitoring
Monitor these key log patterns:
- `CRITICAL SUCCESS: Underdog Service initialized` - Successful startup
- `Login successful on attempt` - Successful authentication
- `Proxy configuration applied` - Proxy working correctly
- `DEVELOPER MODE DETECTED` - Stealth measures needed
- `Location Error (Code: TE)` - Location verification issues

### Performance Monitoring
- Monitor memory usage (should stay under 1GB typically)
- Check response times for API calls
- Monitor proxy bandwidth usage
- Track login success rates

## 🛡️ Security Considerations

### Environment Variables
```bash
# Never commit these to version control
UNDERDOG_USERNAME=<EMAIL>
UNDERDOG_PASSWORD=your_secure_password
PROXY_USERNAME=your_proxy_username
PROXY_PASSWORD=your_proxy_password
```

### Network Security
- Use HTTPS proxies when possible
- Implement firewall rules to restrict access
- Consider VPN for additional security layer
- Regularly rotate proxy credentials

### Application Security
- Run in headless mode in production (`HEADLESS=true`)
- Enable all stealth features (`STEALTH_ENABLED=true`)
- Use strong, unique passwords
- Regularly update dependencies

## 🔧 Troubleshooting Common Deployment Issues

### Issue: "Developer Mode Detected"
**Solution:**
```bash
# Ensure stealth is enabled
STEALTH_ENABLED=true
HEADLESS=true
REMOVE_WEBDRIVER=true

# Clear user data and restart
rm -rf ./user_data
npm start
```

### Issue: Proxy Connection Failed
**Solution:**
```bash
# Test proxy manually
curl --proxy ***********************************:port https://ipinfo.io

# Check proxy configuration
echo $PROXY_SERVER
echo $PROXY_TYPE

# Try different proxy type
PROXY_TYPE=socks5  # or http
```

### Issue: Login Failures
**Solution:**
```bash
# Increase retry attempts
MAX_LOGIN_ATTEMPTS=5
LOGIN_RETRY_DELAY_MS=10000

# Run in debug mode to see what's happening
HEADLESS=false
npm start
```

### Issue: Memory Leaks
**Solution:**
```bash
# Monitor memory usage
ps aux | grep node

# Restart application periodically
pm2 restart underdog-automation

# Increase memory limits if needed
node --max-old-space-size=2048 server.js
```

## 📊 Performance Optimization

### Recommended Settings for Production
```bash
# Optimal configuration for production
HEADLESS=true
STEALTH_ENABLED=true
SESSION_REFRESH_INTERVAL_MINUTES=60
LOCATION_REFRESH_INTERVAL_MINUTES=2
MAX_LOGIN_ATTEMPTS=3
LOGIN_RETRY_DELAY_MS=5000
```

### Resource Management
- Allocate at least 2GB RAM
- Ensure stable internet connection
- Use SSD storage for better performance
- Monitor CPU usage during peak times

## 🆘 Emergency Procedures

### Application Not Responding
```bash
# Check if process is running
ps aux | grep node

# Kill and restart
pkill -f "node server.js"
npm start

# Or with PM2
pm2 restart underdog-automation
```

### Proxy Issues
```bash
# Switch to backup proxy
PROXY_SERVER=backup-proxy-host:port

# Test new proxy
curl --proxy ******************************************:port https://ipinfo.io

# Restart application
npm start
```

### Account Locked
1. Check Underdog Fantasy account status
2. Verify no suspicious activity alerts
3. Contact Underdog Fantasy support if needed
4. Use different credentials if available

## 📞 Support and Maintenance

### Regular Maintenance Tasks
- Weekly: Check logs for errors
- Weekly: Verify proxy connectivity
- Monthly: Update dependencies
- Monthly: Rotate proxy credentials
- Quarterly: Review and update configuration

### Getting Help
1. Check application logs first
2. Review this deployment guide
3. Test individual components (proxy, credentials, etc.)
4. Document error messages and steps to reproduce
5. Check for updates to the application

## 📈 Scaling Considerations

For high-volume usage:
- Consider multiple proxy endpoints
- Implement load balancing
- Use database for session persistence
- Add monitoring and alerting
- Implement rate limiting and queuing
