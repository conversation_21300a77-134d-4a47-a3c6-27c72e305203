{"name": "underdog-simplified-enhanced", "version": "1.4.0", "description": "Enhanced Underdog Fantasy automation with comprehensive location verification debugging", "main": "enhanced-server.js", "scripts": {"start": "node enhanced-server.js", "dev": "node enhanced-server.js", "test": "node test-api.js", "debug": "DEBUG_ENABLED=true node enhanced-server.js", "validate": "node -e \"require('./enhanced-config').validate(); console.log('✅ Configuration valid');\""}, "keywords": ["underdog", "fantasy", "automation", "puppeteer", "enhanced", "debugging", "location-verification"], "dependencies": {"axios": "^1.9.0", "dotenv": "^16.5.0", "express": "^5.1.0", "fs-extra": "^11.3.0", "puppeteer": "^24.8.2", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "puppeteer-extra-plugin-user-preferences": "^2.4.1"}, "engines": {"node": ">=18.0.0"}}