# puppeteer-extra-plugin-user-preferences

> A plugin for [puppeteer-extra](https://github.com/berstend/puppeteer-extra).

### Install

```bash
yarn add puppeteer-extra-plugin-user-preferences
```

## API

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

#### Table of Contents

-   [Plugin](#plugin)

### [Plugin](https://github.com/berstend/puppeteer-extra/blob/db57ea66cf10d407cf63af387892492e495a84f2/packages/puppeteer-extra-plugin-user-preferences/index.js#L30-L73)

**Extends: PuppeteerExtraPlugin**

Launch puppeteer with arbitrary user preferences.

The user defined preferences will be merged with preferences set by other plugins.
Plugins can add user preferences by exposing a data entry with the name `userPreferences`.

Overview:
<https://chromium.googlesource.com/chromium/src/+/master/chrome/common/pref_names.cc>

Type: `function (opts)`

-   `opts` **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)** Options (optional, default `{}`)
    -   `opts.userPrefs` **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)** An object containing the preferences. (optional, default `{}`)

Example:

```javascript
const puppeteer = require('puppeteer-extra')
puppeteer.use(require('puppeteer-extra-plugin-user-preferences')({userPrefs: {
  webkit: {
    webprefs: {
      default_font_size: 22
    }
  }
}}))
const browser = await puppeteer.launch()
```

* * *
