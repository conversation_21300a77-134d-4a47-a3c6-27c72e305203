<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyInterpreterInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="69">
            <item index="0" class="java.lang.String" itemvalue="beautifulsoup4" />
            <item index="1" class="java.lang.String" itemvalue="dj-database-url" />
            <item index="2" class="java.lang.String" itemvalue="django-crispy-forms" />
            <item index="3" class="java.lang.String" itemvalue="tls-client" />
            <item index="4" class="java.lang.String" itemvalue="asgiref" />
            <item index="5" class="java.lang.String" itemvalue="packaging" />
            <item index="6" class="java.lang.String" itemvalue="et-xmlfile" />
            <item index="7" class="java.lang.String" itemvalue="kombu" />
            <item index="8" class="java.lang.String" itemvalue="whitenoise" />
            <item index="9" class="java.lang.String" itemvalue="decouple" />
            <item index="10" class="java.lang.String" itemvalue="gunicorn" />
            <item index="11" class="java.lang.String" itemvalue="sqlparse" />
            <item index="12" class="java.lang.String" itemvalue="path" />
            <item index="13" class="java.lang.String" itemvalue="Faker" />
            <item index="14" class="java.lang.String" itemvalue="Django" />
            <item index="15" class="java.lang.String" itemvalue="certifi" />
            <item index="16" class="java.lang.String" itemvalue="typing_extensions" />
            <item index="17" class="java.lang.String" itemvalue="fake-useragent" />
            <item index="18" class="java.lang.String" itemvalue="soupsieve" />
            <item index="19" class="java.lang.String" itemvalue="pytz" />
            <item index="20" class="java.lang.String" itemvalue="urllib3" />
            <item index="21" class="java.lang.String" itemvalue="idna" />
            <item index="22" class="java.lang.String" itemvalue="openpyxl" />
            <item index="23" class="java.lang.String" itemvalue="charset-normalizer" />
            <item index="24" class="java.lang.String" itemvalue="psycopg2" />
            <item index="25" class="java.lang.String" itemvalue="psycopg2-binary" />
            <item index="26" class="java.lang.String" itemvalue="requests" />
            <item index="27" class="java.lang.String" itemvalue="dataclasses-json" />
            <item index="28" class="java.lang.String" itemvalue="curl_cffi" />
            <item index="29" class="java.lang.String" itemvalue="celery" />
            <item index="30" class="java.lang.String" itemvalue="amqp" />
            <item index="31" class="java.lang.String" itemvalue="cffi" />
            <item index="32" class="java.lang.String" itemvalue="aiohttp" />
            <item index="33" class="java.lang.String" itemvalue="click-repl" />
            <item index="34" class="java.lang.String" itemvalue="uvicorn" />
            <item index="35" class="java.lang.String" itemvalue="click-plugins" />
            <item index="36" class="java.lang.String" itemvalue="aiosignal" />
            <item index="37" class="java.lang.String" itemvalue="click-didyoumean" />
            <item index="38" class="java.lang.String" itemvalue="async-timeout" />
            <item index="39" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="40" class="java.lang.String" itemvalue="mypy-extensions" />
            <item index="41" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="42" class="java.lang.String" itemvalue="h11" />
            <item index="43" class="java.lang.String" itemvalue="marshmallow" />
            <item index="44" class="java.lang.String" itemvalue="wcwidth" />
            <item index="45" class="java.lang.String" itemvalue="pycparser" />
            <item index="46" class="java.lang.String" itemvalue="sniffio" />
            <item index="47" class="java.lang.String" itemvalue="redis" />
            <item index="48" class="java.lang.String" itemvalue="frozenlist" />
            <item index="49" class="java.lang.String" itemvalue="starlette" />
            <item index="50" class="java.lang.String" itemvalue="anyio" />
            <item index="51" class="java.lang.String" itemvalue="vine" />
            <item index="52" class="java.lang.String" itemvalue="billiard" />
            <item index="53" class="java.lang.String" itemvalue="annotated-types" />
            <item index="54" class="java.lang.String" itemvalue="pydantic" />
            <item index="55" class="java.lang.String" itemvalue="six" />
            <item index="56" class="java.lang.String" itemvalue="prompt_toolkit" />
            <item index="57" class="java.lang.String" itemvalue="tzdata" />
            <item index="58" class="java.lang.String" itemvalue="aiohappyeyeballs" />
            <item index="59" class="java.lang.String" itemvalue="orjson" />
            <item index="60" class="java.lang.String" itemvalue="click" />
            <item index="61" class="java.lang.String" itemvalue="attrs" />
            <item index="62" class="java.lang.String" itemvalue="fastapi" />
            <item index="63" class="java.lang.String" itemvalue="pydantic_core" />
            <item index="64" class="java.lang.String" itemvalue="typing-inspect" />
            <item index="65" class="java.lang.String" itemvalue="colorama" />
            <item index="66" class="java.lang.String" itemvalue="propcache" />
            <item index="67" class="java.lang.String" itemvalue="multidict" />
            <item index="68" class="java.lang.String" itemvalue="yarl" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>