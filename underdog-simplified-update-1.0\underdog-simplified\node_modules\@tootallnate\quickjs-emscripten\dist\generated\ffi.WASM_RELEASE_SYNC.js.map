{"version": 3, "file": "ffi.WASM_RELEASE_SYNC.js", "sourceRoot": "", "sources": ["../../ts/generated/ffi.WASM_RELEASE_SYNC.ts"], "names": [], "mappings": ";;;AAIA;;;;;;GAMG;AACH,MAAa,UAAU;IACrB,YAAoB,MAA+B;QAA/B,WAAM,GAAN,MAAM,CAAyB;QACnD,2BAA2B;QAClB,UAAK,GAAG,KAAK,CAAA;QAEtB,cAAS,GACP,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAE/D,iBAAY,GACV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAA;QAEzD,8BAAyB,GACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAE3E,kCAA6B,GAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAEnF,+BAA0B,GACxB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAA;QAEvE,6BAAwB,GACtB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAA;QAE7D,4BAAuB,GACrB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAA;QAE5D,+BAA0B,GACxB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAE5E,qBAAgB,GACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAA;QAErD,gBAAW,GACT,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAA;QAEhD,iBAAY,GACV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAA;QAEjD,gBAAW,GACT,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAA;QAEhD,mBAAc,GACZ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAA;QAEnD,oBAAe,GACb,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,IAAI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAA;QAExD,mBAAc,GACZ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAA;QAE3D,oBAAe,GACb,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,IAAI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAA;QAExD,yBAAoB,GAClB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAEtE,gCAA2B,GACzB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAE7E,wBAAmB,GACjB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAErE,oBAAe,GACb,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAEjE,wBAAmB,GACjB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAEzE,kBAAa,GACX,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAA;QAE1D,uBAAkB,GAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAExE,iBAAY,GACV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAA;QAEzD,mBAAc,GACZ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAEpE,mBAAc,GACZ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAEpE,kBAAa,GACX,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAEnE,kBAAa,GACX,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAEnE,kBAAa,GACX,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAE5E,kCAA6B,GAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAEnF,uBAAkB,GAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAExE,qBAAgB,GACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAA;QAE7D,0BAAqB,GACnB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAEpF,gBAAW,GACT,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAE1E,gBAAW,GACT,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAE/E,mBAAc,GACZ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,SAAS,EAAC,SAAS,CAAC,CAAC,CAAA;QAElI,aAAQ,GACN,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAEzF,yBAAoB,GAClB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAE1E,aAAQ,GACN,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAE9D,aAAQ,GACN,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAEzF,eAAU,GACR,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAEhE,wBAAmB,GACjB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAA;QAEhE,6BAAwB,GACtB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAE9E,sBAAiB,GACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,IAAI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAA;QAE1D,qBAAgB,GACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAA;QAErD,wBAAmB,GACjB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAA;QAExD,oBAAe,GACb,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAE9E,mCAA8B,GAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAEpF,sCAAiC,GAC/B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,IAAI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAA;QAE1E,uCAAkC,GAChC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,IAAI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAA;QAE3E,kCAA6B,GAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,CAAA;QAE/E,mCAA8B,GAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,IAAI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAA;IA9JjB,CAAC;CA+JxD;AAhKD,gCAgKC", "sourcesContent": ["// This file generated by \"generate.ts ffi\" in the root of the repo.\nimport { QuickJSEmscriptenModule } from \"../emscripten-types\"\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>oint<PERSON>, <PERSON><PERSON><PERSON>xtPointer, J<PERSON>ontextPointerPointer, JSModuleDefPointer, <PERSON><PERSON><PERSON><PERSON><PERSON>oint<PERSON>, J<PERSON><PERSON>ueConstPointer, J<PERSON><PERSON>uePointerPointer, JSValueConstPointerPointer, QTS_C_To_HostCallbackFuncPointer, QTS_C_To_HostInterruptFuncPointer, QTS_C_To_HostLoadModuleFuncPointer, BorrowedHeapCharPointer, OwnedHeapCharPointer, JSBorrowedCharPointer, JSVoidPointer, EvalFlags, EvalDetectModule } from \"../types-ffi\"\n\n/**\n * Low-level FFI bindings to QuickJS's Emscripten module.\n * See instead [[QuickJSContext]], the public Javascript interface exposed by this\n * library.\n *\n * @unstable The FFI interface is considered private and may change.\n */\nexport class QuickJSFFI {\n  constructor(private module: QuickJSEmscriptenModule) {}\n  /** Set at compile time. */\n  readonly DEBUG = false\n\n  QTS_Throw: (ctx: JSContextPointer, error: JSValuePointer | JSValueConstPointer) => JSValuePointer =\n    this.module.cwrap(\"QTS_Throw\", \"number\", [\"number\",\"number\"])\n\n  QTS_NewError: (ctx: JSContextPointer) => JSValuePointer =\n    this.module.cwrap(\"QTS_NewError\", \"number\", [\"number\"])\n\n  QTS_RuntimeSetMemoryLimit: (rt: JSRuntimePointer, limit: number) => void =\n    this.module.cwrap(\"QTS_RuntimeSetMemoryLimit\", null, [\"number\",\"number\"])\n\n  QTS_RuntimeComputeMemoryUsage: (rt: JSRuntimePointer, ctx: JSContextPointer) => JSValuePointer =\n    this.module.cwrap(\"QTS_RuntimeComputeMemoryUsage\", \"number\", [\"number\",\"number\"])\n\n  QTS_RuntimeDumpMemoryUsage: (rt: JSRuntimePointer) => OwnedHeapCharPointer =\n    this.module.cwrap(\"QTS_RuntimeDumpMemoryUsage\", \"number\", [\"number\"])\n\n  QTS_RecoverableLeakCheck: () => number =\n    this.module.cwrap(\"QTS_RecoverableLeakCheck\", \"number\", [])\n\n  QTS_BuildIsSanitizeLeak: () => number =\n    this.module.cwrap(\"QTS_BuildIsSanitizeLeak\", \"number\", [])\n\n  QTS_RuntimeSetMaxStackSize: (rt: JSRuntimePointer, stack_size: number) => void =\n    this.module.cwrap(\"QTS_RuntimeSetMaxStackSize\", null, [\"number\",\"number\"])\n\n  QTS_GetUndefined: () => JSValueConstPointer =\n    this.module.cwrap(\"QTS_GetUndefined\", \"number\", [])\n\n  QTS_GetNull: () => JSValueConstPointer =\n    this.module.cwrap(\"QTS_GetNull\", \"number\", [])\n\n  QTS_GetFalse: () => JSValueConstPointer =\n    this.module.cwrap(\"QTS_GetFalse\", \"number\", [])\n\n  QTS_GetTrue: () => JSValueConstPointer =\n    this.module.cwrap(\"QTS_GetTrue\", \"number\", [])\n\n  QTS_NewRuntime: () => JSRuntimePointer =\n    this.module.cwrap(\"QTS_NewRuntime\", \"number\", [])\n\n  QTS_FreeRuntime: (rt: JSRuntimePointer) => void =\n    this.module.cwrap(\"QTS_FreeRuntime\", null, [\"number\"])\n\n  QTS_NewContext: (rt: JSRuntimePointer) => JSContextPointer =\n    this.module.cwrap(\"QTS_NewContext\", \"number\", [\"number\"])\n\n  QTS_FreeContext: (ctx: JSContextPointer) => void =\n    this.module.cwrap(\"QTS_FreeContext\", null, [\"number\"])\n\n  QTS_FreeValuePointer: (ctx: JSContextPointer, value: JSValuePointer) => void =\n    this.module.cwrap(\"QTS_FreeValuePointer\", null, [\"number\",\"number\"])\n\n  QTS_FreeValuePointerRuntime: (rt: JSRuntimePointer, value: JSValuePointer) => void =\n    this.module.cwrap(\"QTS_FreeValuePointerRuntime\", null, [\"number\",\"number\"])\n\n  QTS_FreeVoidPointer: (ctx: JSContextPointer, ptr: JSVoidPointer) => void =\n    this.module.cwrap(\"QTS_FreeVoidPointer\", null, [\"number\",\"number\"])\n\n  QTS_FreeCString: (ctx: JSContextPointer, str: JSBorrowedCharPointer) => void =\n    this.module.cwrap(\"QTS_FreeCString\", null, [\"number\",\"number\"])\n\n  QTS_DupValuePointer: (ctx: JSContextPointer, val: JSValuePointer | JSValueConstPointer) => JSValuePointer =\n    this.module.cwrap(\"QTS_DupValuePointer\", \"number\", [\"number\",\"number\"])\n\n  QTS_NewObject: (ctx: JSContextPointer) => JSValuePointer =\n    this.module.cwrap(\"QTS_NewObject\", \"number\", [\"number\"])\n\n  QTS_NewObjectProto: (ctx: JSContextPointer, proto: JSValuePointer | JSValueConstPointer) => JSValuePointer =\n    this.module.cwrap(\"QTS_NewObjectProto\", \"number\", [\"number\",\"number\"])\n\n  QTS_NewArray: (ctx: JSContextPointer) => JSValuePointer =\n    this.module.cwrap(\"QTS_NewArray\", \"number\", [\"number\"])\n\n  QTS_NewFloat64: (ctx: JSContextPointer, num: number) => JSValuePointer =\n    this.module.cwrap(\"QTS_NewFloat64\", \"number\", [\"number\",\"number\"])\n\n  QTS_GetFloat64: (ctx: JSContextPointer, value: JSValuePointer | JSValueConstPointer) => number =\n    this.module.cwrap(\"QTS_GetFloat64\", \"number\", [\"number\",\"number\"])\n\n  QTS_NewString: (ctx: JSContextPointer, string: BorrowedHeapCharPointer) => JSValuePointer =\n    this.module.cwrap(\"QTS_NewString\", \"number\", [\"number\",\"number\"])\n\n  QTS_GetString: (ctx: JSContextPointer, value: JSValuePointer | JSValueConstPointer) => JSBorrowedCharPointer =\n    this.module.cwrap(\"QTS_GetString\", \"number\", [\"number\",\"number\"])\n\n  QTS_NewSymbol: (ctx: JSContextPointer, description: BorrowedHeapCharPointer, isGlobal: number) => JSValuePointer =\n    this.module.cwrap(\"QTS_NewSymbol\", \"number\", [\"number\",\"number\",\"number\"])\n\n  QTS_GetSymbolDescriptionOrKey: (ctx: JSContextPointer, value: JSValuePointer | JSValueConstPointer) => JSBorrowedCharPointer =\n    this.module.cwrap(\"QTS_GetSymbolDescriptionOrKey\", \"number\", [\"number\",\"number\"])\n\n  QTS_IsGlobalSymbol: (ctx: JSContextPointer, value: JSValuePointer | JSValueConstPointer) => number =\n    this.module.cwrap(\"QTS_IsGlobalSymbol\", \"number\", [\"number\",\"number\"])\n\n  QTS_IsJobPending: (rt: JSRuntimePointer) => number =\n    this.module.cwrap(\"QTS_IsJobPending\", \"number\", [\"number\"])\n\n  QTS_ExecutePendingJob: (rt: JSRuntimePointer, maxJobsToExecute: number, lastJobContext: JSContextPointerPointer) => JSValuePointer =\n    this.module.cwrap(\"QTS_ExecutePendingJob\", \"number\", [\"number\",\"number\",\"number\"])\n\n  QTS_GetProp: (ctx: JSContextPointer, this_val: JSValuePointer | JSValueConstPointer, prop_name: JSValuePointer | JSValueConstPointer) => JSValuePointer =\n    this.module.cwrap(\"QTS_GetProp\", \"number\", [\"number\",\"number\",\"number\"])\n\n  QTS_SetProp: (ctx: JSContextPointer, this_val: JSValuePointer | JSValueConstPointer, prop_name: JSValuePointer | JSValueConstPointer, prop_value: JSValuePointer | JSValueConstPointer) => void =\n    this.module.cwrap(\"QTS_SetProp\", null, [\"number\",\"number\",\"number\",\"number\"])\n\n  QTS_DefineProp: (ctx: JSContextPointer, this_val: JSValuePointer | JSValueConstPointer, prop_name: JSValuePointer | JSValueConstPointer, prop_value: JSValuePointer | JSValueConstPointer, get: JSValuePointer | JSValueConstPointer, set: JSValuePointer | JSValueConstPointer, configurable: boolean, enumerable: boolean, has_value: boolean) => void =\n    this.module.cwrap(\"QTS_DefineProp\", null, [\"number\",\"number\",\"number\",\"number\",\"number\",\"number\",\"boolean\",\"boolean\",\"boolean\"])\n\n  QTS_Call: (ctx: JSContextPointer, func_obj: JSValuePointer | JSValueConstPointer, this_obj: JSValuePointer | JSValueConstPointer, argc: number, argv_ptrs: JSValueConstPointerPointer) => JSValuePointer =\n    this.module.cwrap(\"QTS_Call\", \"number\", [\"number\",\"number\",\"number\",\"number\",\"number\"])\n\n  QTS_ResolveException: (ctx: JSContextPointer, maybe_exception: JSValuePointer) => JSValuePointer =\n    this.module.cwrap(\"QTS_ResolveException\", \"number\", [\"number\",\"number\"])\n\n  QTS_Dump: (ctx: JSContextPointer, obj: JSValuePointer | JSValueConstPointer) => JSBorrowedCharPointer =\n    this.module.cwrap(\"QTS_Dump\", \"number\", [\"number\",\"number\"])\n\n  QTS_Eval: (ctx: JSContextPointer, js_code: BorrowedHeapCharPointer, filename: string, detectModule: EvalDetectModule, evalFlags: EvalFlags) => JSValuePointer =\n    this.module.cwrap(\"QTS_Eval\", \"number\", [\"number\",\"number\",\"string\",\"number\",\"number\"])\n\n  QTS_Typeof: (ctx: JSContextPointer, value: JSValuePointer | JSValueConstPointer) => OwnedHeapCharPointer =\n    this.module.cwrap(\"QTS_Typeof\", \"number\", [\"number\",\"number\"])\n\n  QTS_GetGlobalObject: (ctx: JSContextPointer) => JSValuePointer =\n    this.module.cwrap(\"QTS_GetGlobalObject\", \"number\", [\"number\"])\n\n  QTS_NewPromiseCapability: (ctx: JSContextPointer, resolve_funcs_out: JSValuePointerPointer) => JSValuePointer =\n    this.module.cwrap(\"QTS_NewPromiseCapability\", \"number\", [\"number\",\"number\"])\n\n  QTS_TestStringArg: (string: string) => void =\n    this.module.cwrap(\"QTS_TestStringArg\", null, [\"string\"])\n\n  QTS_BuildIsDebug: () => number =\n    this.module.cwrap(\"QTS_BuildIsDebug\", \"number\", [])\n\n  QTS_BuildIsAsyncify: () => number =\n    this.module.cwrap(\"QTS_BuildIsAsyncify\", \"number\", [])\n\n  QTS_NewFunction: (ctx: JSContextPointer, func_id: number, name: string) => JSValuePointer =\n    this.module.cwrap(\"QTS_NewFunction\", \"number\", [\"number\",\"number\",\"string\"])\n\n  QTS_ArgvGetJSValueConstPointer: (argv: JSValuePointer | JSValueConstPointer, index: number) => JSValueConstPointer =\n    this.module.cwrap(\"QTS_ArgvGetJSValueConstPointer\", \"number\", [\"number\",\"number\"])\n\n  QTS_RuntimeEnableInterruptHandler: (rt: JSRuntimePointer) => void =\n    this.module.cwrap(\"QTS_RuntimeEnableInterruptHandler\", null, [\"number\"])\n\n  QTS_RuntimeDisableInterruptHandler: (rt: JSRuntimePointer) => void =\n    this.module.cwrap(\"QTS_RuntimeDisableInterruptHandler\", null, [\"number\"])\n\n  QTS_RuntimeEnableModuleLoader: (rt: JSRuntimePointer, use_custom_normalize: number) => void =\n    this.module.cwrap(\"QTS_RuntimeEnableModuleLoader\", null, [\"number\",\"number\"])\n\n  QTS_RuntimeDisableModuleLoader: (rt: JSRuntimePointer) => void =\n    this.module.cwrap(\"QTS_RuntimeDisableModuleLoader\", null, [\"number\"])\n}\n"]}