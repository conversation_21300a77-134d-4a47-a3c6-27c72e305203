/**
 * Simple test script for the Underdog Fantasy Automation API
 */
const axios = require('axios');

// Sample player IDs for testing (provided test IDs)
const betIds = [
  "55875246-0e29-4387-a20e-9a0ef9a968ba", // Test player ID 1
  "e0847a4a-4aa6-4b3a-b42f-6bbe7a81f86f", // Test player ID 2
  "a763577d-d951-45bf-897d-e8264f0db863"  // Test player ID 3
];

// API endpoint
const API_URL = 'http://localhost:3000/generate-links';

// Function to test the API
async function testApi() {
  console.log('Testing Underdog Fantasy Automation API...');
  console.log(`API URL: ${API_URL}`);
  console.log(`Player IDs: ${JSON.stringify(betIds, null, 2)}`);

  try {
    console.log('Sending request to API...');
    const response = await axios.post(API_URL, { betIds });

    console.log('Response received:');
    console.log(JSON.stringify(response.data, null, 2));

    // Check if the response contains a share link
    if (response.data && response.data.share_link) {
      console.log(`\nSuccess! Share link: ${response.data.share_link}`);
    } else if (response.data && response.data.error) {
      console.error(`\nError: ${response.data.error}`);
    } else {
      console.error('\nUnexpected response format:', response.data);
    }
  } catch (error) {
    console.error('Error testing API:');

    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error(`Status: ${error.response.status}`);
      console.error(`Data: ${JSON.stringify(error.response.data, null, 2)}`);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received from server');
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Error setting up request:', error.message);
    }
  }
}

// Run the test
testApi().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
