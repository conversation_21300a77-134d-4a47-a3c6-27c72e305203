{"name": "underdog-fantasy-automation", "version": "1.3.0", "description": "Enhanced Underdog Fantasy Automation Service with Proxy Support", "main": "server.js", "scripts": {"start": "node server.js", "test": "node test-api.js"}, "dependencies": {"axios": "^1.9.0", "dotenv": "^16.5.0", "express": "^5.1.0", "fs-extra": "^11.3.0", "puppeteer": "^24.8.2", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "puppeteer-extra-plugin-user-preferences": "^2.4.1"}, "engines": {"node": ">=18.0.0"}}