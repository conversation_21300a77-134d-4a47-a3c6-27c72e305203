const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const fs = require('fs').promises;
const path = require('path');

// Enable stealth mode
puppeteer.use(StealthPlugin());

class EnhancedUnderdogService {
  constructor(config) {
    this.config = config;
    this.browser = null;
    this.page = null;
    this.sessionData = {
      isLoggedIn: false,
      locationVerified: false,
      cookies: null,
      tokens: {}
    };
    this.debugDir = './debug_artifacts';
    this.retryCount = 0;
    this.maxRetries = 3;
  }

  async initialize() {
    try {
      await this.ensureDebugDirectory();
      await this.launchBrowser();
      await this.setupPage();

      const loginSuccess = await this.performLogin();
      if (!loginSuccess) {
        throw new Error('Login failed during initialization');
      }

      const locationSuccess = await this.verifyLocation();
      if (!locationSuccess) {
        console.warn('Location verification failed, but continuing...');
      }

      console.log('✅ CRITICAL SUCCESS: Enhanced Underdog service initialized');
      return true;
    } catch (error) {
      console.error('❌ CRITICAL FAILURE: Service initialization failed:', error);
      await this.captureDebugArtifacts('initialization_failure');
      throw error;
    }
  }

  async launchBrowser() {
    const args = [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--no-first-run',
      '--no-zygote',
      '--disable-gpu',
      '--window-size=1366,768',

      // Anti-detection measures
      '--disable-blink-features=AutomationControlled',
      '--disable-features=VizDisplayCompositor',
      '--disable-web-security',
      '--disable-features=TranslateUI',
      '--disable-ipc-flooding-protection',
      '--disable-renderer-backgrounding',
      '--disable-backgrounding-occluded-windows',
      '--disable-client-side-phishing-detection',
      '--disable-component-extensions-with-background-pages',
      '--disable-default-apps',
      '--disable-extensions',
      '--disable-hang-monitor',
      '--disable-prompt-on-repost',
      '--disable-sync',
      '--metrics-recording-only',
      '--no-default-browser-check',
      '--no-pings',
      '--password-store=basic',
      '--use-mock-keychain',
      '--force-color-profile=srgb'
    ];

    // Add proxy configuration if provided
    if (this.config.proxy) {
      args.push(`--proxy-server=${this.config.proxy.server}`);
      if (this.config.proxy.bypass) {
        args.push(`--proxy-bypass-list=${this.config.proxy.bypass}`);
      }
    }

    console.log('🚀 Launching browser with enhanced stealth configuration...');
    this.browser = await puppeteer.launch({
      headless: this.config.headless || false,
      args,
      ignoreDefaultArgs: ['--enable-automation'],
      defaultViewport: null
    });

    console.log('✅ Browser launched successfully');
  }

  async setupPage() {
    this.page = await this.browser.newPage();

    // Set up proxy authentication if needed
    if (this.config.proxy && this.config.proxy.username) {
      await this.page.authenticate({
        username: this.config.proxy.username,
        password: this.config.proxy.password
      });
      console.log('🔐 Proxy authentication configured');
    }

    // Enhanced stealth measures
    await this.page.setUserAgent(
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36'
    );

    await this.page.setViewport({
      width: 1366,
      height: 768,
      deviceScaleFactor: 1
    });

    // Remove automation indicators
    await this.page.evaluateOnNewDocument(() => {
      Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined,
      });

      // Mock chrome runtime
      window.chrome = {
        runtime: {},
      };

      // Override permissions API
      const originalQuery = window.navigator.permissions.query;
      window.navigator.permissions.query = (parameters) => (
        parameters.name === 'notifications' ?
          Promise.resolve({ state: Notification.permission }) :
          originalQuery(parameters)
      );

      // Mock plugins
      Object.defineProperty(navigator, 'plugins', {
        get: () => [1, 2, 3, 4, 5],
      });
    });

    // Set geolocation to US
    await this.page.setGeolocation({
      latitude: 40.7128,
      longitude: -74.0060,
      accuracy: 100
    });

    // Set timezone
    await this.page.emulateTimezone('America/New_York');

    // Set up request/response monitoring
    await this.setupNetworkMonitoring();

    console.log('✅ Page setup completed with enhanced stealth measures');
  }

  async setupNetworkMonitoring() {
    await this.page.setRequestInterception(true);

    this.page.on('request', (request) => {
      // Log important requests
      const url = request.url();
      if (url.includes('api') || url.includes('auth') || url.includes('location')) {
        console.log(`📤 Request: ${request.method()} ${url.substring(0, 100)}...`);
      }
      request.continue();
    });

    this.page.on('response', async (response) => {
      const url = response.url();
      const status = response.status();

      if (url.includes('api') || url.includes('auth') || url.includes('location')) {
        console.log(`📥 Response: ${status} ${url.substring(0, 100)}...`);

        // Capture tokens from responses
        if (status === 200 && url.includes('auth')) {
          try {
            const responseText = await response.text();
            if (responseText.includes('token') || responseText.includes('access_token')) {
              console.log('🔑 Auth token detected in response');
              // Store token logic here
            }
          } catch (e) {
            // Ignore errors reading response
          }
        }
      }
    });

    this.page.on('console', (msg) => {
      if (msg.type() === 'error') {
        console.log(`🔴 Browser Console Error: ${msg.text()}`);
      }
    });

    this.page.on('pageerror', (error) => {
      console.log(`🔴 Page Error: ${error.message}`);
    });
  }

  async performLogin() {
    try {
      console.log('🔐 Starting enhanced login process...');

      // Navigate to login page
      await this.page.goto('https://underdogfantasy.com/pick-em/higher-lower/all/home', {
        waitUntil: 'networkidle2',
        timeout: 30000
      });

      await this.captureDebugArtifacts('before_login');

      // Check if already logged in
      const isLoggedIn = await this.checkLoginStatus();
      if (isLoggedIn) {
        console.log('✅ Already logged in');
        this.sessionData.isLoggedIn = true;
        return true;
      }

      // Wait for login form
      console.log('⏳ Waiting for login form...');
      await this.page.waitForSelector('input[placeholder="Email"]', {
        visible: true,
        timeout: 15000
      });

      // Human-like typing with delays
      await this.humanType('input[placeholder="Email"]', this.config.credentials.email);
      await this.randomDelay(500, 1500);

      await this.humanType('input[type="password"]', this.config.credentials.password);
      await this.randomDelay(500, 1500);

      await this.captureDebugArtifacts('form_filled');

      // Submit form
      console.log('📤 Submitting login form...');
      await Promise.all([
        this.page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 30000 }),
        this.page.click('button[type="submit"], button.styles__button__E1IZ_')
      ]);

      await this.captureDebugArtifacts('after_login_submit');

      // Verify login success
      const loginSuccess = await this.verifyLoginSuccess();
      if (loginSuccess) {
        this.sessionData.isLoggedIn = true;
        this.sessionData.cookies = await this.page.cookies();
        console.log('✅ Login successful');
        return true;
      } else {
        throw new Error('Login verification failed');
      }

    } catch (error) {
      console.error('❌ Login failed:', error);
      await this.captureDebugArtifacts('login_failure');
      return false;
    }
  }

  async verifyLoginSuccess() {
    try {
      // Multiple checks for login success
      const checks = await this.page.evaluate(() => {
        return {
          // Check URL - should not be on login page
          notOnLoginPage: !window.location.href.includes('/login'),

          // Check for user-specific elements
          hasUserElements: !!(
            document.querySelector('.profile, .user-menu, .account') ||
            document.querySelector('[data-testid*="user"], [data-testid*="profile"]')
          ),

          // Check for absence of login form
          noLoginForm: !document.querySelector('input[placeholder="Email"]'),

          // Check for main content
          hasMainContent: !!(
            document.querySelector('.styles__overUnderCell__by1xI') ||
            document.querySelector('[data-testid*="pick"], [data-testid*="bet"]')
          ),

          // Check page title
          pageTitle: document.title,

          // Check for error messages
          hasErrors: !!(
            document.querySelector('.error, .alert-error, [role="alert"]') ||
            document.querySelector('.styles__errorMessage__3Aq0_')
          )
        };
      });

      console.log('🔍 Login verification checks:', JSON.stringify(checks, null, 2));

      // Calculate confidence score
      const positiveChecks = [
        checks.notOnLoginPage,
        checks.hasUserElements,
        checks.noLoginForm,
        checks.hasMainContent
      ].filter(Boolean).length;

      const confidence = (positiveChecks / 4) * 100;
      console.log(`📊 Login confidence: ${confidence}%`);

      return confidence >= 75 && !checks.hasErrors;
    } catch (error) {
      console.error('❌ Error verifying login:', error);
      return false;
    }
  }

  async verifyLocation() {
    try {
      console.log('📍 Starting comprehensive location verification...');

      // Navigate to location-sensitive page
      await this.page.goto('https://underdogfantasy.com/pick-em/higher-lower/all/home', {
        waitUntil: 'networkidle2',
        timeout: 30000
      });

      await this.captureDebugArtifacts('location_verification_start');

      // Wait for page to fully load and process location
      await this.randomDelay(2000, 4000);

      // Comprehensive location verification checks
      const locationStatus = await this.performLocationChecks();

      if (locationStatus.verified) {
        console.log('✅ Location verification successful');
        this.sessionData.locationVerified = true;
        return true;
      } else {
        console.log('❌ Location verification failed:', locationStatus.reason);
        await this.captureDebugArtifacts('location_verification_failed');

        // Attempt location recovery
        const recoverySuccess = await this.attemptLocationRecovery();
        if (recoverySuccess) {
          this.sessionData.locationVerified = true;
          return true;
        }

        return false;
      }
    } catch (error) {
      console.error('❌ Location verification error:', error);
      await this.captureDebugArtifacts('location_verification_error');
      return false;
    }
  }

  async performLocationChecks() {
    try {
      // Wait for potential location processing
      await this.page.waitForTimeout(3000);

      const checks = await this.page.evaluate(() => {
        const results = {
          // Primary success indicators
          hasPickEmContent: !!(
            document.querySelector('.styles__overUnderCell__by1xI') ||
            document.querySelector('[data-testid*="pick"]') ||
            document.querySelector('.pick-em, .bet-card')
          ),

          // Error indicators
          hasLocationError: !!(
            document.querySelector('[class*="location"][class*="error"]') ||
            document.querySelector('[data-testid*="location"][data-testid*="error"]') ||
            document.querySelector('.geo-error, .location-blocked')
          ),

          hasGeneralError: !!(
            document.querySelector('.error, .alert-error, [role="alert"]') ||
            document.querySelector('.styles__errorMessage__3Aq0_')
          ),

          // Developer mode error
          hasDeveloperModeError: !!(
            document.querySelector('*').textContent?.includes('developer mode') ||
            document.querySelector('*').textContent?.includes('user_dismiss')
          ),

          // Loading states
          isLoading: !!(
            document.querySelector('.loading, .spinner, [data-testid*="loading"]') ||
            document.querySelector('[class*="loading"]')
          ),

          // Page state
          currentUrl: window.location.href,
          pageTitle: document.title,

          // Content availability
          contentCount: document.querySelectorAll('.styles__overUnderCell__by1xI').length,

          // Check for location-related modals or overlays
          hasModal: !!(
            document.querySelector('.modal, .overlay, .popup') ||
            document.querySelector('[role="dialog"]')
          )
        };

        return results;
      });

      console.log('🔍 Location verification checks:', JSON.stringify(checks, null, 2));

      // Determine verification status
      if (checks.hasDeveloperModeError) {
        return { verified: false, reason: 'Developer mode error detected' };
      }

      if (checks.hasLocationError) {
        return { verified: false, reason: 'Location error detected' };
      }

      if (checks.hasGeneralError) {
        return { verified: false, reason: 'General error detected' };
      }

      if (checks.isLoading) {
        // Wait a bit more for loading to complete
        await this.page.waitForTimeout(5000);
        return await this.performLocationChecks(); // Recursive check
      }

      if (checks.hasPickEmContent && checks.contentCount > 0) {
        return { verified: true, reason: 'Pick-em content available' };
      }

      return { verified: false, reason: 'No pick-em content found' };

    } catch (error) {
      console.error('❌ Error performing location checks:', error);
      return { verified: false, reason: `Check error: ${error.message}` };
    }
  }

  async attemptLocationRecovery() {
    console.log('🔄 Attempting location verification recovery...');

    try {
      // Strategy 1: Scroll and interact with page
      await this.page.evaluate(() => {
        window.scrollTo(0, document.body.scrollHeight / 2);
      });
      await this.randomDelay(1000, 2000);

      // Strategy 2: Click on any location-related elements
      await this.page.evaluate(() => {
        const locationElements = document.querySelectorAll('[data-testid*="location"], [class*="location"]');
        locationElements.forEach(el => {
          if (el.click) el.click();
        });
      });
      await this.randomDelay(2000, 3000);

      // Strategy 3: Refresh the page
      await this.page.reload({ waitUntil: 'networkidle2' });
      await this.randomDelay(3000, 5000);

      // Re-check location status
      const recoveryStatus = await this.performLocationChecks();

      if (recoveryStatus.verified) {
        console.log('✅ Location recovery successful');
        return true;
      } else {
        console.log('❌ Location recovery failed');
        return false;
      }

    } catch (error) {
      console.error('❌ Location recovery error:', error);
      return false;
    }
  }

  async checkLoginStatus() {
    try {
      // Quick check for login indicators
      const isLoggedIn = await this.page.evaluate(() => {
        // Check for login form presence (indicates not logged in)
        const hasLoginForm = !!document.querySelector('input[placeholder="Email"]');

        // Check for user-specific content
        const hasUserContent = !!(
          document.querySelector('.profile, .user-menu, .account') ||
          document.querySelector('.styles__overUnderCell__by1xI')
        );

        return !hasLoginForm && hasUserContent;
      });

      return isLoggedIn;
    } catch (error) {
      console.error('❌ Error checking login status:', error);
      return false;
    }
  }

  async humanType(selector, text) {
    try {
      await this.page.focus(selector);
      await this.randomDelay(100, 300);

      // Clear existing text
      await this.page.keyboard.down('Control');
      await this.page.keyboard.press('KeyA');
      await this.page.keyboard.up('Control');
      await this.page.keyboard.press('Delete');

      // Type with human-like delays
      for (const char of text) {
        await this.page.keyboard.type(char);
        await this.randomDelay(50, 150);
      }

      await this.randomDelay(200, 500);
    } catch (error) {
      console.error(`❌ Error typing in ${selector}:`, error);
      throw error;
    }
  }

  async randomDelay(min, max) {
    const delay = Math.floor(Math.random() * (max - min + 1)) + min;
    await this.page.waitForTimeout(delay);
  }

  async ensureDebugDirectory() {
    try {
      await fs.mkdir(this.debugDir, { recursive: true });
    } catch (error) {
      console.error('❌ Error creating debug directory:', error);
    }
  }

  async captureDebugArtifacts(prefix) {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const baseFilename = `${prefix}_${timestamp}`;

      // Capture screenshot
      const screenshotPath = path.join(this.debugDir, `${baseFilename}.png`);
      await this.page.screenshot({
        path: screenshotPath,
        fullPage: true
      });

      // Capture HTML
      const htmlPath = path.join(this.debugDir, `${baseFilename}.html`);
      const html = await this.page.content();
      await fs.writeFile(htmlPath, html);

      // Capture page info
      const infoPath = path.join(this.debugDir, `${baseFilename}_info.json`);
      const pageInfo = await this.page.evaluate(() => ({
        url: window.location.href,
        title: document.title,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
        cookies: document.cookie,
        localStorage: Object.keys(localStorage).reduce((acc, key) => {
          acc[key] = localStorage.getItem(key);
          return acc;
        }, {}),
        sessionStorage: Object.keys(sessionStorage).reduce((acc, key) => {
          acc[key] = sessionStorage.getItem(key);
          return acc;
        }, {})
      }));

      await fs.writeFile(infoPath, JSON.stringify(pageInfo, null, 2));

      console.log(`📸 Debug artifacts saved: ${baseFilename}`);
    } catch (error) {
      console.error('❌ Error capturing debug artifacts:', error);
    }
  }

  async refreshLocation() {
    try {
      console.log('🔄 Starting location refresh...');

      if (!this.sessionData.isLoggedIn) {
        console.log('❌ Not logged in, cannot refresh location');
        return false;
      }

      // Check current login status before proceeding
      const stillLoggedIn = await this.checkLoginStatus();
      if (!stillLoggedIn) {
        console.log('❌ Session lost, attempting re-login...');
        const reloginSuccess = await this.performLogin();
        if (!reloginSuccess) {
          console.log('❌ Re-login failed');
          this.sessionData.isLoggedIn = false;
          return false;
        }
      }

      // Perform location verification
      const locationSuccess = await this.verifyLocation();

      if (locationSuccess) {
        console.log('✅ Location refresh successful');
        this.sessionData.locationVerified = true;
        return true;
      } else {
        console.log('❌ Location refresh failed');
        this.sessionData.locationVerified = false;
        return false;
      }

    } catch (error) {
      console.error('❌ Location refresh error:', error);
      await this.captureDebugArtifacts('location_refresh_error');
      return false;
    }
  }

  async shutdown() {
    try {
      console.log('🔄 Shutting down service...');

      if (this.page) {
        await this.page.close();
        this.page = null;
      }

      if (this.browser) {
        await this.browser.close();
        this.browser = null;
      }

      console.log('✅ Service shutdown complete');
    } catch (error) {
      console.error('❌ Error during shutdown:', error);
    }
  }

  // Session management methods
  async maintainSession() {
    setInterval(async () => {
      try {
        console.log('🔄 Performing session maintenance...');

        const loginStatus = await this.checkLoginStatus();
        if (!loginStatus) {
          console.log('⚠️ Session lost, attempting recovery...');
          const recoverySuccess = await this.performLogin();
          if (recoverySuccess) {
            console.log('✅ Session recovery successful');
          } else {
            console.log('❌ Session recovery failed');
          }
        }

        // Refresh location periodically
        if (this.sessionData.isLoggedIn) {
          await this.refreshLocation();
        }

      } catch (error) {
        console.error('❌ Session maintenance error:', error);
      }
    }, 5 * 60 * 1000); // Every 5 minutes
  }
}

module.exports = EnhancedUnderdogService;
