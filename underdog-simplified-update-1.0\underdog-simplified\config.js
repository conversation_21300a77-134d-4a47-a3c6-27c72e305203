/**
 * Simple configuration for Underdog Fantasy Automation
 */
require('dotenv').config();

// Default values
const DEFAULT_PORT = 3000;
const DEFAULT_LOGIN_TIMEOUT = 60000; // 1 minute
const DEFAULT_NAVIGATION_TIMEOUT = 45000; // 45 seconds
const DEFAULT_SESSION_REFRESH_INTERVAL = 7200000; // 2 hours (120 minutes)
const DEFAULT_LOCATION_REFRESH_INTERVAL = 240000; // 4 minutes (much more frequent than session refresh)
const DEFAULT_EXPECTED_IDS_PER_BET = 3; // Default number of player IDs in a bet

// Helper function to parse integer with default value
function parseInteger(value, defaultValue) {
  if (value === undefined || value === null || value === '') {
    return defaultValue;
  }

  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
}

// Helper function to parse boolean
function parseBoolean(value, defaultValue) {
  if (value === undefined || value === null || value === '') {
    return defaultValue;
  }

  if (typeof value === 'boolean') {
    return value;
  }

  if (typeof value === 'string') {
    return value.toLowerCase() === 'true' || value === '1';
  }

  return defaultValue;
}

// Configuration object
const config = {
  // Server configuration
  server: {
    port: parseInteger(process.env.PORT, DEFAULT_PORT),
  },

  // Credentials
  credentials: {
    username: process.env.UNDERDOG_USERNAME ? process.env.UNDERDOG_USERNAME.trim() : '',
    password: process.env.UNDERDOG_PASSWORD ? process.env.UNDERDOG_PASSWORD.trim() : '',
  },

  // Browser configuration
  browser: {
    headless: parseBoolean(process.env.HEADLESS, true),
    userDataDir: process.env.USER_DATA_DIR || './user_data',
  },

  // Proxy configuration
  proxy: {
    server: process.env.PROXY_SERVER || '',
    username: process.env.PROXY_USERNAME || '',
    password: process.env.PROXY_PASSWORD || '',
    type: process.env.PROXY_TYPE || 'http', // http, https, socks4, socks5
    // Format proxy URL with authentication if credentials are provided
    get formattedServer() {
      if (!this.server) return '';

      const protocol = this.type === 'socks4' || this.type === 'socks5' ? this.type : 'http';

      if (this.username && this.password) {
        // Use format: protocol://username:password@host:port
        return `${protocol}://${this.username}:${this.password}@${this.server}`;
      }
      return `${protocol}://${this.server}`;
    },
  },

  // Stealth configuration
  stealth: {
    enabled: parseBoolean(process.env.STEALTH_ENABLED, true),
    removeWebdriver: parseBoolean(process.env.REMOVE_WEBDRIVER, true),
    fakeUserAgent: parseBoolean(process.env.FAKE_USER_AGENT, true),
    fakeViewport: parseBoolean(process.env.FAKE_VIEWPORT, true),
    fakeLanguages: parseBoolean(process.env.FAKE_LANGUAGES, true),
    fakeTimezone: parseBoolean(process.env.FAKE_TIMEZONE, false), // Keep false to use real timezone for location
  },

  // Retry configuration
  retry: {
    maxLoginAttempts: parseInteger(process.env.MAX_LOGIN_ATTEMPTS, 3),
    loginRetryDelay: parseInteger(process.env.LOGIN_RETRY_DELAY_MS, 5000),
    maxLocationRefreshAttempts: parseInteger(process.env.MAX_LOCATION_REFRESH_ATTEMPTS, 2),
  },

  // Timeouts
  timeouts: {
    login: parseInteger(process.env.LOGIN_TIMEOUT_MS, DEFAULT_LOGIN_TIMEOUT),
    navigation: parseInteger(process.env.NAVIGATION_TIMEOUT_MS, DEFAULT_NAVIGATION_TIMEOUT),
    sessionRefresh: parseInteger(process.env.SESSION_REFRESH_INTERVAL_MINUTES, 120) * 60 * 1000,
    locationRefresh: parseInteger(process.env.LOCATION_REFRESH_INTERVAL_MINUTES, 4) * 60 * 1000,
  },

  // Bet configuration
  bet: {
    expectedIdsPerBet: parseInteger(process.env.EXPECTED_IDS_PER_BET, DEFAULT_EXPECTED_IDS_PER_BET),
  },

  // Endpoints
  endpoints: {
    underdogLogin: 'https://underdogfantasy.com/pick-em/higher-lower/all/home',
    underdogPickem: 'https://underdogfantasy.com/pick-em/higher-lower/all/home',
    underdogLobby: 'https://underdogfantasy.com/pick-em/higher-lower/all/home', // Used for location verification
    underdogBetSlip: 'https://api.underdogfantasy.com/v5/entry_slips',
    underdogShareLink: 'https://api.underdogfantasy.com/v1/entry_slips/{id}/share_link',
  },

  // Selectors
  selectors: {
    login: {
      emailInput: "input[placeholder='Email']",
      passwordInput: "input[placeholder='Password']",
      submitButton: "button.styles__button__E1IZ_",
      successIndicator: ".styles__overUnderCell__by1xI",
      errorMessage: ".styles__errorMessage__3Aq0_",
    },
  },
};

// Validate configuration
function validateConfig() {
  const errors = [];

  // Check required credentials
  if (!config.credentials.username) {
    errors.push('UNDERDOG_USERNAME is required');
  }

  if (!config.credentials.password) {
    errors.push('UNDERDOG_PASSWORD is required');
  }

  // If there are errors, log them and exit
  if (errors.length > 0) {
    console.error('Configuration errors:');
    errors.forEach(error => console.error(`- ${error}`));
    return false;
  }

  return true;
}

module.exports = { config, validateConfig };
