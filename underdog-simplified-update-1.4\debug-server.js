const express = require('express');
require('dotenv').config();

console.log('🚀 Starting debug server...');
console.log('📊 Configuration check:');
console.log(`   - Proxy: ${process.env.PROXY_SERVER || 'Not configured'}`);
console.log(`   - Username: ${process.env.UNDERDOG_USERNAME || 'Not configured'}`);
console.log(`   - Password: ${process.env.UNDERDOG_PASSWORD ? 'Configured' : 'Not configured'}`);
console.log(`   - Headless: ${process.env.HEADLESS || 'false'}`);

const app = express();
const port = process.env.PORT || 3000;

app.use(express.json());

// Add request logging
app.use((req, res, next) => {
  console.log(`📥 ${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

let serviceStatus = {
  status: 'initializing',
  initialized: false,
  startTime: Date.now(),
  error: null,
  loginAttempts: 0,
  lastLoginAttempt: null
};

// Health check endpoint
app.get('/health', (req, res) => {
  console.log('🔍 Health check requested');
  const uptime = (Date.now() - serviceStatus.startTime) / 1000;
  
  const response = {
    ...serviceStatus,
    uptime: uptime,
    timestamp: new Date().toISOString()
  };
  
  console.log(`📊 Health status: ${response.status}`);
  res.json(response);
});

// Generate links endpoint
app.post('/generate-links', async (req, res) => {
  console.log('🎯 Generate links requested');
  console.log('📝 Request body:', JSON.stringify(req.body, null, 2));
  
  const { betIds } = req.body;
  
  if (!betIds || !Array.isArray(betIds)) {
    console.log('❌ Invalid request: betIds array required');
    return res.status(400).json({
      share_link: null,
      error: 'Invalid request: betIds array required'
    });
  }

  console.log(`🎲 Processing ${betIds.length} bet IDs: ${betIds.join(', ')}`);

  // Check service status
  if (serviceStatus.status !== 'ready') {
    console.log(`⚠️ Service not ready: ${serviceStatus.status}`);
    return res.status(503).json({
      share_link: null,
      error: `Service Unavailable: ${serviceStatus.error || serviceStatus.status}`
    });
  }

  try {
    // Simulate the actual bet link generation process
    console.log('🔄 Simulating bet link generation...');
    
    // Mock successful response for testing
    const shareLink = `https://underdogfantasy.com/pick-em/share/${betIds.join('-')}-${Date.now()}`;
    
    console.log(`✅ Generated share link: ${shareLink}`);
    
    res.json({
      share_link: shareLink,
      error: null,
      processed_bet_ids: betIds,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Error generating bet link:', error);
    res.status(500).json({
      share_link: null,
      error: `Internal server error: ${error.message}`
    });
  }
});

// Debug endpoint
app.get('/debug', (req, res) => {
  console.log('🔧 Debug info requested');
  
  const debugInfo = {
    serviceStatus,
    environment: {
      proxy_server: process.env.PROXY_SERVER,
      proxy_configured: !!process.env.PROXY_SERVER,
      credentials_configured: !!(process.env.UNDERDOG_USERNAME && process.env.UNDERDOG_PASSWORD),
      headless: process.env.HEADLESS,
      port: process.env.PORT
    },
    system: {
      node_version: process.version,
      platform: process.platform,
      uptime: process.uptime(),
      memory: process.memoryUsage()
    },
    timestamp: new Date().toISOString()
  };
  
  res.json(debugInfo);
});

// Start HTTP server first
console.log('🌐 Starting HTTP server...');
const server = app.listen(port, () => {
  console.log(`✅ HTTP server running on port ${port}`);
  console.log(`📊 Health check: http://localhost:${port}/health`);
  console.log(`🎯 API endpoint: http://localhost:${port}/generate-links`);
  console.log(`🔧 Debug endpoint: http://localhost:${port}/debug`);
});

// Initialize Underdog service in background with timeout
console.log('🔄 Starting Underdog service initialization...');

async function initializeUnderdogService() {
  try {
    serviceStatus.status = 'initializing';
    serviceStatus.loginAttempts++;
    serviceStatus.lastLoginAttempt = new Date().toISOString();
    
    console.log('🔧 Loading Underdog service...');
    
    // Add timeout to prevent hanging
    const initTimeout = setTimeout(() => {
      console.error('⏰ Initialization timeout after 60 seconds');
      serviceStatus.status = 'error';
      serviceStatus.error = 'Initialization timeout - Puppeteer hanging';
      serviceStatus.initialized = false;
    }, 60000);
    
    const UnderdogService = require('./underdogService');
    console.log('📦 UnderdogService loaded');
    
    const config = require('./config');
    console.log('⚙️ Config loaded');
    
    console.log('🚀 Creating service instance...');
    const underdogService = new UnderdogService();
    
    console.log('🔄 Initializing service...');
    await underdogService.initialize();
    
    clearTimeout(initTimeout);
    
    serviceStatus.status = 'ready';
    serviceStatus.initialized = true;
    serviceStatus.error = null;
    
    console.log('✅ CRITICAL SUCCESS: Underdog service initialized successfully!');
    
  } catch (error) {
    console.error('❌ CRITICAL FAILURE: Service initialization failed:', error.message);
    console.error('📋 Error details:', error);
    
    serviceStatus.status = 'error';
    serviceStatus.initialized = false;
    serviceStatus.error = error.message;
  }
}

// Start initialization in background
initializeUnderdogService().catch(error => {
  console.error('❌ Background initialization failed:', error);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🔄 Shutting down debug server...');
  server.close(() => {
    console.log('✅ Server shutdown complete');
    process.exit(0);
  });
});

console.log('🎉 Debug server setup complete - monitoring initialization...');
