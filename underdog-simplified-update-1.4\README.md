# Underdog Fantasy Automation - v1.4

## 🎯 Enhanced User-Location-Token Capture

This version specifically addresses the **User-Location-Token** capture issue that was preventing successful bet link generation. The system now includes comprehensive GeoComply integration and enhanced token monitoring.

## 🚀 Quick Start

### 1. Install Dependencies
```bash
npm install
```

### 2. Configure Environment
Edit `.env` file with your settings:

```env
# Underdog Fantasy Credentials
UNDERDOG_USERNAME=<EMAIL>
UNDERDOG_PASSWORD=your_password

# Proxy Configuration (REQUIRED for geo-restricted access)
PROXY_SERVER=your_proxy_ip:port
PROXY_USERNAME=proxy_username
PROXY_PASSWORD=proxy_password
PROXY_TYPE=http

# Server Configuration
PORT=3000
HEADLESS=false
```

### 3. Start Server
```bash
node server.js
```

### 4. Test API
```bash
curl -X POST http://localhost:3000/generate-links \
  -H "Content-Type: application/json" \
  -d '{"betIds":["bet-id-1","bet-id-2","bet-id-3"]}'
```

## 🔧 Key Features

### Enhanced Token Capture
- **Multiple monitoring sources**: Headers, cookies, JavaScript variables, localStorage
- **Progressive verification**: Multi-page navigation to trigger GeoComply
- **Extended timeouts**: 45-second wait with 10-second retry intervals
- **Comprehensive debugging**: Detailed logs and screenshots

### Proxy Support
- **Required for geo-restrictions**: Automatically uses US-based proxy
- **Authentication**: Full proxy credential support
- **IP verification**: Automatic proxy connectivity testing

### Browser Automation
- **Stealth measures**: Anti-detection features enabled
- **Human-like behavior**: Random delays and typing patterns
- **Error recovery**: Automatic retry logic with fallbacks

## 📊 API Endpoints

### Health Check
```
GET /health
```
Response:
```json
{
  "status": "ready|initializing|error",
  "initialized": true|false,
  "uptime": 123.45,
  "timestamp": "2025-05-24T16:00:00.000Z"
}
```

### Generate Bet Links
```
POST /generate-links
```
Request:
```json
{
  "betIds": [
    "55875246-0e29-4387-a20e-9a0ef9a968ba",
    "e0847a4a-4aa6-4b3a-b42f-6bbe7a81f86f",
    "a763577d-d951-45bf-897d-e8264f0db863"
  ]
}
```

Success Response:
```json
{
  "share_link": "https://underdogfantasy.com/pick-em/share/...",
  "error": null
}
```

Error Response:
```json
{
  "share_link": null,
  "error": "Detailed error message"
}
```

## 🔍 Debugging Features

### Screenshot Capture
- Login process screenshots
- Token capture failure screenshots
- Error state documentation

### Detailed Logging
- Network request/response monitoring
- Token capture progress tracking
- JavaScript execution logs
- GeoComply interaction details

### Debug Files
- `email-typed-*.png` - Email entry verification
- `password-typed-*.png` - Password entry verification
- `login-form-filled-*.png` - Complete form state
- `token-capture-failed-*.png` - Token capture failures
- `*.html` - Page content dumps for analysis

## 🛠️ Troubleshooting

### Common Issues

#### 1. Login Failed During Initialization
- **Cause**: Proxy connectivity or credential issues
- **Solution**: Check proxy settings and credentials in `.env`
- **Debug**: Check `login-error-*.png` screenshots

#### 2. User-Location-Token Not Captured
- **Cause**: GeoComply not triggered or token in unexpected location
- **Solution**: System now includes comprehensive token scanning
- **Debug**: Monitor console logs for `[TokenCapture]` messages

#### 3. Service Unavailable
- **Cause**: Browser automation detected or session expired
- **Solution**: System includes enhanced stealth measures
- **Debug**: Check browser screenshots and HTML dumps

### Debug Commands
```bash
# Check if server is running
curl http://localhost:3000/health

# Test with sample bet IDs
curl -X POST http://localhost:3000/generate-links \
  -H "Content-Type: application/json" \
  -d '{"betIds":["55875246-0e29-4387-a20e-9a0ef9a968ba"]}'
```

## 📁 File Structure

```
underdog-simplified-update-1.4/
├── server.js              # HTTP server
├── underdogService.js      # Main automation service
├── config.js              # Configuration settings
├── package.json           # Dependencies
├── .env                   # Environment variables
├── README.md              # This file
└── CHANGELOG.md           # Version changes
```

## 🔐 Security Notes

- Credentials are stored in `.env` file (not committed to git)
- Proxy authentication is handled securely
- Browser user data is cleared on each startup
- Session tokens are managed automatically

## 🎯 Testing

The system has been tested with the provided bet IDs:
- `55875246-0e29-4387-a20e-9a0ef9a968ba`
- `e0847a4a-4aa6-4b3a-b42f-6bbe7a81f86f`
- `a763577d-d951-45bf-897d-e8264f0db863`

Expected behavior:
1. ✅ Proxy connection established (New York IP)
2. ✅ Login successful with provided credentials
3. ✅ Authorization token captured
4. ✅ Device ID captured
5. ✅ **User-Location-Token captured** (NEW in v1.4)
6. ✅ Bet link generation successful

## 📞 Support

For issues or questions:
1. Check the debug screenshots and logs
2. Verify proxy connectivity
3. Confirm credentials are correct
4. Review the CHANGELOG.md for recent updates

## 🔄 Version History

- **v1.4**: Enhanced User-Location-Token capture with comprehensive GeoComply integration
- **v1.3**: Improved proxy support and error handling
- **v1.2**: Added stealth measures and retry logic
- **v1.1**: Basic automation with login functionality
- **v1.0**: Initial release
