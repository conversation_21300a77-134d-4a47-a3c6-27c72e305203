{"name": "puppeteer-extra", "version": "3.3.6", "description": "Teach puppeteer new tricks through plugins.", "repository": "berstend/puppeteer-extra", "author": "be<PERSON><PERSON>", "license": "MIT", "typings": "dist/index.d.ts", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "files": ["dist"], "scripts": {"clean": "rimraf dist/*", "prebuild": "run-s clean", "build": "run-s build:tsc build:rollup ambient-dts", "build:tsc": "tsc --module commonjs", "build:rollup": "rollup -c rollup.config.ts", "docs": "documentation readme --quiet --shallow --github --markdown-theme transitivebs --readme-file readme.md --section API ./src/index.ts", "postdocs": "npx prettier --write readme.md", "test:ts": "ava -v --config ava.config-ts.js", "test:js": "ava -v --serial --concurrency 1 --fail-fast", "test": "run-p test:js test:ts", "test-ci": "run-s test", "ambient-dts": "run-s ambient-dts-copy ambient-dts-fix-path", "ambient-dts-copy": "copyfiles -u 1 \"src/**/*.d.ts\" dist", "ambient-dts-fix-path": "replace-in-files --string='/// <reference path=\"../src/' --replacement='/// <reference path=\"../dist/' 'dist/**/*.d.ts'"}, "keywords": ["puppeteer", "puppeteer-extra", "flash", "stealth", "prefs", "user-preferences", "chrome", "headless", "pupeteer"], "engines": {"node": ">=8"}, "devDependencies": {"@types/node": "^18.0.0", "@types/puppeteer": "*", "ava": "^2.4.0", "documentation-markdown-themes": "^12.1.5", "npm-run-all": "^4.1.5", "puppeteer": "^10.2.0", "puppeteer-extra-plugin": "^3.2.3", "puppeteer-extra-plugin-anonymize-ua": "^2.4.6", "rimraf": "^3.0.0", "rollup": "^1.27.5", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-plugin-typescript2": "^0.25.2", "ts-node": "^8.5.4", "tslint": "^5.20.1", "tslint-config-prettier": "^1.18.0", "tslint-config-standard": "^9.0.0", "typescript": "4.4.3"}, "dependencies": {"@types/debug": "^4.1.0", "debug": "^4.1.1", "deepmerge": "^4.2.2"}, "peerDependencies": {"@types/puppeteer": "*", "puppeteer": "*", "puppeteer-core": "*"}, "peerDependenciesMeta": {"puppeteer": {"optional": true}, "puppeteer-core": {"optional": true}, "@types/puppeteer": {"optional": true}}, "gitHead": "39248f1f5deeb21b1e7eb6ae07b8ef73f1231ab9"}