# 🌐 Proxy Setup Guide for Underdog Fantasy Automation

## Quick Start for Restricted Regions

If you're testing from outside the US, follow this guide to set up proxy support for accessing Underdog Fantasy.

## 📋 Prerequisites

1. **US-based Proxy Server**: You need a proxy server with a US IP address
2. **Proxy Credentials**: Username and password (if required by your proxy provider)
3. **Updated Application**: Ensure you're using version 1.3+ with proxy support

## 🔧 Configuration Steps

### Step 1: Choose Your Proxy Type

The application supports multiple proxy protocols:
- **HTTP/HTTPS**: Most common, works with most providers
- **SOCKS4**: Faster, good for basic needs
- **SOCKS5**: Most advanced, best performance and security

### Step 2: Configure Environment Variables

Edit your `.env` file and add the following proxy settings:

```bash
# Basic proxy configuration
PROXY_SERVER=your-proxy-host:port
PROXY_TYPE=http

# If your proxy requires authentication
PROXY_USERNAME=your_username
PROXY_PASSWORD=your_password
```

### Step 3: Example Configurations

#### Example 1: HTTP Proxy with Authentication
```bash
PROXY_SERVER=us-proxy.example.com:8080
PROXY_USERNAME=myusername
PROXY_PASSWORD=mypassword
PROXY_TYPE=http
```

#### Example 2: SOCKS5 Proxy
```bash
PROXY_SERVER=socks.example.com:1080
PROXY_USERNAME=myusername
PROXY_PASSWORD=mypassword
PROXY_TYPE=socks5
```

#### Example 3: HTTP Proxy without Authentication
```bash
PROXY_SERVER=*************:3128
PROXY_TYPE=http
```

## 🧪 Testing Your Proxy

### Method 1: Using the Application
1. Start the application with your proxy configuration
2. Check the logs for proxy connection messages
3. Look for "Proxy configuration applied" in the startup logs

### Method 2: Manual Testing
Test your proxy manually before using it with the application:

```bash
# Test HTTP proxy
curl --proxy ***********************************:port https://ipinfo.io

# Test SOCKS5 proxy
curl --socks5 username:password@proxy-host:port https://ipinfo.io
```

## 🛡️ Security Best Practices

1. **Use HTTPS**: Always use HTTPS proxies when possible
2. **Rotate Proxies**: Consider using different proxy IPs periodically
3. **Monitor Usage**: Keep track of your proxy bandwidth usage
4. **Secure Credentials**: Store proxy credentials securely in environment variables

## 🔍 Troubleshooting

### Common Proxy Issues

#### Connection Refused
- Verify proxy server address and port
- Check if proxy server is online
- Ensure firewall isn't blocking the connection

#### Authentication Failed
- Double-check username and password
- Verify proxy provider account is active
- Check for special characters in credentials (may need URL encoding)

#### Slow Performance
- Try different proxy servers from your provider
- Switch from HTTP to SOCKS5 for better performance
- Check proxy server load and location

#### IP Not from US
- Verify your proxy provider offers US-based IPs
- Request a specific US region if available
- Test the proxy IP location using ipinfo.io

## 📊 Recommended Proxy Providers

### Premium Options (Recommended for Production)
1. **Bright Data** (formerly Luminati)
   - High-quality residential proxies
   - Excellent US coverage
   - Premium pricing

2. **Oxylabs**
   - Datacenter and residential proxies
   - Good performance and reliability
   - Professional support

### Budget-Friendly Options
1. **ProxyMesh**
   - Affordable rotating proxies
   - Good for testing
   - Simple setup

2. **Storm Proxies**
   - Budget-friendly pricing
   - Basic but functional
   - Good for development

### Free Options (Testing Only)
- **Note**: Free proxies are generally unreliable and not recommended for production use
- Use only for initial testing and development

## 🚀 Advanced Configuration

### Multiple Proxy Support
For advanced users, you can implement proxy rotation by modifying the configuration:

```bash
# Primary proxy
PROXY_SERVER=proxy1.example.com:8080
PROXY_USERNAME=user1
PROXY_PASSWORD=pass1

# You can implement rotation logic in your deployment scripts
```

### Proxy Health Monitoring
The application includes built-in proxy health monitoring:
- Connection testing during startup
- Automatic retry on proxy failures
- Detailed error logging for troubleshooting

## 📝 Configuration Template

Copy this template to your `.env` file and fill in your proxy details:

```bash
# Underdog Fantasy Credentials
UNDERDOG_USERNAME=<EMAIL>
UNDERDOG_PASSWORD=your_password

# Proxy Configuration (Required for restricted regions)
PROXY_SERVER=your-proxy-host:port
PROXY_USERNAME=your_proxy_username
PROXY_PASSWORD=your_proxy_password
PROXY_TYPE=http

# Optional: Enhanced Settings
STEALTH_ENABLED=true
HEADLESS=true
MAX_LOGIN_ATTEMPTS=3
```

## 🆘 Getting Help

If you encounter issues with proxy setup:

1. **Check Logs**: Look for detailed error messages in the application logs
2. **Test Manually**: Use curl or browser to test proxy connectivity
3. **Contact Provider**: Reach out to your proxy provider for support
4. **Debug Mode**: Run with `HEADLESS=false` to see browser behavior

## 📞 Support

For additional help with proxy configuration:
- Check the main README.md for troubleshooting tips
- Review the application logs for specific error messages
- Test your proxy configuration with simple tools first
